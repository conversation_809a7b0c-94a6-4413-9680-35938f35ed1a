package isimado.knowledgeagent.tool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import isimado.knowledgeagent.tool.entity.dao.EntityType;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【entity_type】的数据库操作Mapper
* @createDate 2025-05-19 15:19:53
* @Entity isimado.knowledgeagent.domain.entity.dao.EntityType
*/
public interface EntityTypeMapper extends BaseMapper<EntityType> {

    List<EntityType> vectorRetrieval(String embeddingString);
}




