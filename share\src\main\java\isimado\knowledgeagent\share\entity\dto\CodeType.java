//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#Code
 *
 * <p>CodeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="CodeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionTemporalAssociationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AllegianceRelationshipCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CommandRelationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DesiredEffectCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EchelonCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}HostilityStatusCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OperationalStatusCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OrganizationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhaseCompletionCondition"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReinforcedReducedType"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SecurityClassificationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskFunctionalAssociationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TimeReferenceCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}WeaponRuleOfEngagementCode"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class CodeType {

    @JsonProperty(value = "ActionCode")
    protected ActionCodeType actionCode;
    @JsonProperty(value = "ActionTemporalAssociationCode")

    protected ActionTemporalAssociationCodeType actionTemporalAssociationCode;
    @JsonProperty(value = "AllegianceRelationshipCode")

    protected AllegianceRelationshipCodeType allegianceRelationshipCode;
    @JsonProperty(value = "CommandRelationCode")

    protected CommandRelationCodeType commandRelationCode;
    @JsonProperty(value = "DesiredEffectCode")

    protected DesiredEffectCodeType desiredEffectCode;
    @JsonProperty(value = "EchelonCode")

    protected EchelonCodeType echelonCode;
    @JsonProperty(value = "HostilityStatusCode")

    protected HostilityStatusCodeType hostilityStatusCode;
    @JsonProperty(value = "OperationalStatusCode")

    protected OperationalStatusCodeType operationalStatusCode;
    @JsonProperty(value = "OrganizationCode")
    protected OrganizationCodeType organizationCode;
    @JsonProperty(value = "PlanPhaseCompletionCondition")

    protected PlanPhaseCompletionConditionType planPhaseCompletionCondition;
    @JsonProperty(value = "ReinforcedReducedType")

    protected ReinforcedReducedTypeType reinforcedReducedType;
    @JsonProperty(value = "SecurityClassificationCode")

    protected SecurityClassificationCodeType securityClassificationCode;
    @JsonProperty(value = "TaskFunctionalAssociationCode")

    protected TaskFunctionalAssociationCodeType taskFunctionalAssociationCode;
    @JsonProperty(value = "TimeReferenceCode")

    protected TimeReferenceCodeType timeReferenceCode;
    @JsonProperty(value = "WeaponRuleOfEngagementCode")

    protected WeaponRuleOfEngagementCodeType weaponRuleOfEngagementCode;

    /**
     * 获取actionCode属性的值。
     *
     * @return possible object is
     * {@link ActionCodeType }
     */
    public ActionCodeType getActionCode() {
        return actionCode;
    }

    /**
     * 设置actionCode属性的值。
     *
     * @param value allowed object is
     *              {@link ActionCodeType }
     */
    public void setActionCode(ActionCodeType value) {
        this.actionCode = value;
    }

    /**
     * 获取actionTemporalAssociationCode属性的值。
     *
     * @return possible object is
     * {@link ActionTemporalAssociationCodeType }
     */
    public ActionTemporalAssociationCodeType getActionTemporalAssociationCode() {
        return actionTemporalAssociationCode;
    }

    /**
     * 设置actionTemporalAssociationCode属性的值。
     *
     * @param value allowed object is
     *              {@link ActionTemporalAssociationCodeType }
     */
    public void setActionTemporalAssociationCode(ActionTemporalAssociationCodeType value) {
        this.actionTemporalAssociationCode = value;
    }

    /**
     * 获取allegianceRelationshipCode属性的值。
     *
     * @return possible object is
     * {@link AllegianceRelationshipCodeType }
     */
    public AllegianceRelationshipCodeType getAllegianceRelationshipCode() {
        return allegianceRelationshipCode;
    }

    /**
     * 设置allegianceRelationshipCode属性的值。
     *
     * @param value allowed object is
     *              {@link AllegianceRelationshipCodeType }
     */
    public void setAllegianceRelationshipCode(AllegianceRelationshipCodeType value) {
        this.allegianceRelationshipCode = value;
    }

    /**
     * 获取commandRelationCode属性的值。
     *
     * @return possible object is
     * {@link CommandRelationCodeType }
     */
    public CommandRelationCodeType getCommandRelationCode() {
        return commandRelationCode;
    }

    /**
     * 设置commandRelationCode属性的值。
     *
     * @param value allowed object is
     *              {@link CommandRelationCodeType }
     */
    public void setCommandRelationCode(CommandRelationCodeType value) {
        this.commandRelationCode = value;
    }

    /**
     * 获取desiredEffectCode属性的值。
     *
     * @return possible object is
     * {@link DesiredEffectCodeType }
     */
    public DesiredEffectCodeType getDesiredEffectCode() {
        return desiredEffectCode;
    }

    /**
     * 设置desiredEffectCode属性的值。
     *
     * @param value allowed object is
     *              {@link DesiredEffectCodeType }
     */
    public void setDesiredEffectCode(DesiredEffectCodeType value) {
        this.desiredEffectCode = value;
    }

    /**
     * 获取echelonCode属性的值。
     *
     * @return possible object is
     * {@link EchelonCodeType }
     */
    public EchelonCodeType getEchelonCode() {
        return echelonCode;
    }

    /**
     * 设置echelonCode属性的值。
     *
     * @param value allowed object is
     *              {@link EchelonCodeType }
     */
    public void setEchelonCode(EchelonCodeType value) {
        this.echelonCode = value;
    }

    /**
     * 获取hostilityStatusCode属性的值。
     *
     * @return possible object is
     * {@link HostilityStatusCodeType }
     */
    public HostilityStatusCodeType getHostilityStatusCode() {
        return hostilityStatusCode;
    }

    /**
     * 设置hostilityStatusCode属性的值。
     *
     * @param value allowed object is
     *              {@link HostilityStatusCodeType }
     */
    public void setHostilityStatusCode(HostilityStatusCodeType value) {
        this.hostilityStatusCode = value;
    }

    /**
     * 获取operationalStatusCode属性的值。
     *
     * @return possible object is
     * {@link OperationalStatusCodeType }
     */
    public OperationalStatusCodeType getOperationalStatusCode() {
        return operationalStatusCode;
    }

    /**
     * 设置operationalStatusCode属性的值。
     *
     * @param value allowed object is
     *              {@link OperationalStatusCodeType }
     */
    public void setOperationalStatusCode(OperationalStatusCodeType value) {
        this.operationalStatusCode = value;
    }

    /**
     * 获取organizationCode属性的值。
     *
     * @return possible object is
     * {@link OrganizationCodeType }
     */
    public OrganizationCodeType getOrganizationCode() {
        return organizationCode;
    }

    /**
     * 设置organizationCode属性的值。
     *
     * @param value allowed object is
     *              {@link OrganizationCodeType }
     */
    public void setOrganizationCode(OrganizationCodeType value) {
        this.organizationCode = value;
    }

    /**
     * 获取planPhaseCompletionCondition属性的值。
     *
     * @return possible object is
     * {@link PlanPhaseCompletionConditionType }
     */
    public PlanPhaseCompletionConditionType getPlanPhaseCompletionCondition() {
        return planPhaseCompletionCondition;
    }

    /**
     * 设置planPhaseCompletionCondition属性的值。
     *
     * @param value allowed object is
     *              {@link PlanPhaseCompletionConditionType }
     */
    public void setPlanPhaseCompletionCondition(PlanPhaseCompletionConditionType value) {
        this.planPhaseCompletionCondition = value;
    }

    /**
     * 获取reinforcedReducedType属性的值。
     *
     * @return possible object is
     * {@link ReinforcedReducedTypeType }
     */
    public ReinforcedReducedTypeType getReinforcedReducedType() {
        return reinforcedReducedType;
    }

    /**
     * 设置reinforcedReducedType属性的值。
     *
     * @param value allowed object is
     *              {@link ReinforcedReducedTypeType }
     */
    public void setReinforcedReducedType(ReinforcedReducedTypeType value) {
        this.reinforcedReducedType = value;
    }

    /**
     * 获取securityClassificationCode属性的值。
     *
     * @return possible object is
     * {@link SecurityClassificationCodeType }
     */
    public SecurityClassificationCodeType getSecurityClassificationCode() {
        return securityClassificationCode;
    }

    /**
     * 设置securityClassificationCode属性的值。
     *
     * @param value allowed object is
     *              {@link SecurityClassificationCodeType }
     */
    public void setSecurityClassificationCode(SecurityClassificationCodeType value) {
        this.securityClassificationCode = value;
    }

    /**
     * 获取taskFunctionalAssociationCode属性的值。
     *
     * @return possible object is
     * {@link TaskFunctionalAssociationCodeType }
     */
    public TaskFunctionalAssociationCodeType getTaskFunctionalAssociationCode() {
        return taskFunctionalAssociationCode;
    }

    /**
     * 设置taskFunctionalAssociationCode属性的值。
     *
     * @param value allowed object is
     *              {@link TaskFunctionalAssociationCodeType }
     */
    public void setTaskFunctionalAssociationCode(TaskFunctionalAssociationCodeType value) {
        this.taskFunctionalAssociationCode = value;
    }

    /**
     * 获取timeReferenceCode属性的值。
     *
     * @return possible object is
     * {@link TimeReferenceCodeType }
     */
    public TimeReferenceCodeType getTimeReferenceCode() {
        return timeReferenceCode;
    }

    /**
     * 设置timeReferenceCode属性的值。
     *
     * @param value allowed object is
     *              {@link TimeReferenceCodeType }
     */
    public void setTimeReferenceCode(TimeReferenceCodeType value) {
        this.timeReferenceCode = value;
    }

    /**
     * 获取weaponRuleOfEngagementCode属性的值。
     *
     * @return possible object is
     * {@link WeaponRuleOfEngagementCodeType }
     */
    public WeaponRuleOfEngagementCodeType getWeaponRuleOfEngagementCode() {
        return weaponRuleOfEngagementCode;
    }

    /**
     * 设置weaponRuleOfEngagementCode属性的值。
     *
     * @param value allowed object is
     *              {@link WeaponRuleOfEngagementCodeType }
     */
    public void setWeaponRuleOfEngagementCode(WeaponRuleOfEngagementCodeType value) {
        this.weaponRuleOfEngagementCode = value;
    }

}
