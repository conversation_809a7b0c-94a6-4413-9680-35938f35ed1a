//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>CommandRelationCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="CommandRelationCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ADMCON"/>
 *     &lt;enumeration value="ALTFOR"/>
 *     &lt;enumeration value="ASGND"/>
 *     &lt;enumeration value="ATPRCL"/>
 *     &lt;enumeration value="ATTACH"/>
 *     &lt;enumeration value="CASP1"/>
 *     &lt;enumeration value="CASP2"/>
 *     &lt;enumeration value="CASP3"/>
 *     &lt;enumeration value="CASP4"/>
 *     &lt;enumeration value="CLSSUP"/>
 *     &lt;enumeration value="COAUTH"/>
 *     &lt;enumeration value="COMD"/>
 *     &lt;enumeration value="DETACH"/>
 *     &lt;enumeration value="DIRSUP"/>
 *     &lt;enumeration value="FORCE"/>
 *     &lt;enumeration value="GENSRI"/>
 *     &lt;enumeration value="GENSUP"/>
 *     &lt;enumeration value="HSCPTD"/>
 *     &lt;enumeration value="HSNSPT"/>
 *     &lt;enumeration value="ISCPER"/>
 *     &lt;enumeration value="LOGCON"/>
 *     &lt;enumeration value="MUTSUP"/>
 *     &lt;enumeration value="ONCALL"/>
 *     &lt;enumeration value="OPCOMD"/>
 *     &lt;enumeration value="OPCON"/>
 *     &lt;enumeration value="ORGANC"/>
 *     &lt;enumeration value="REINFC"/>
 *     &lt;enumeration value="RESERV"/>
 *     &lt;enumeration value="ROLE"/>
 *     &lt;enumeration value="SAME"/>
 *     &lt;enumeration value="SPTAMM"/>
 *     &lt;enumeration value="SPTENG"/>
 *     &lt;enumeration value="SPTMED"/>
 *     &lt;enumeration value="SPTMVT"/>
 *     &lt;enumeration value="SPTPAR"/>
 *     &lt;enumeration value="SPTPOL"/>
 *     &lt;enumeration value="SPTPST"/>
 *     &lt;enumeration value="SPTRAT"/>
 *     &lt;enumeration value="SPTRRE"/>
 *     &lt;enumeration value="SPTSPL"/>
 *     &lt;enumeration value="SPTTRN"/>
 *     &lt;enumeration value="TACCNT"/>
 *     &lt;enumeration value="TACCOM"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum CommandRelationCodeType {

    ADMCON("ADMCON"),
    ALTFOR("ALTFOR"),
    ASGND("ASGND"),
    ATPRCL("ATPRCL"),
    ATTACH("ATTACH"),
    CASP_1("CASP1"),
    CASP_2("CASP2"),
    CASP_3("CASP3"),
    CASP_4("CASP4"),
    CLSSUP("CLSSUP"),
    COAUTH("COAUTH"),
    COMD("COMD"),
    DETACH("DETACH"),
    DIRSUP("DIRSUP"),
    FORCE("FORCE"),
    GENSRI("GENSRI"),
    GENSUP("GENSUP"),
    HSCPTD("HSCPTD"),
    HSNSPT("HSNSPT"),
    ISCPER("ISCPER"),
    LOGCON("LOGCON"),
    MUTSUP("MUTSUP"),
    ONCALL("ONCALL"),
    OPCOMD("OPCOMD"),
    OPCON("OPCON"),
    ORGANC("ORGANC"),
    REINFC("REINFC"),
    RESERV("RESERV"),
    ROLE("ROLE"),
    SAME("SAME"),
    SPTAMM("SPTAMM"),
    SPTENG("SPTENG"),
    SPTMED("SPTMED"),
    SPTMVT("SPTMVT"),
    SPTPAR("SPTPAR"),
    SPTPOL("SPTPOL"),
    SPTPST("SPTPST"),
    SPTRAT("SPTRAT"),
    SPTRRE("SPTRRE"),
    SPTSPL("SPTSPL"),
    SPTTRN("SPTTRN"),
    TACCNT("TACCNT"),
    TACCOM("TACCOM");
    private final String value;

    CommandRelationCodeType(String v) {
        value = v;
    }

    public static CommandRelationCodeType fromValue(String v) {
        for (CommandRelationCodeType c : CommandRelationCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
