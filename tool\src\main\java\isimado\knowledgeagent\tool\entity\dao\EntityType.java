package isimado.knowledgeagent.tool.entity.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import isimado.knowledgeagent.tool.context.mybatis.PGVectorTypeHandler;
import lombok.Data;

/**
 * 
 * @TableName entity_type
 */
@TableName(value ="entity_type")
@Data
public class EntityType {
    /**
     * 
     */
    @TableId
    private Integer id;

    /**
     * 
     */
    private String kind;

    /**
     * 
     */
    private String domain;

    /**
     * 
     */
    private String country;

    /**
     * 
     */
    private String category;

    /**
     * 
     */
    private String subcategory;

    /**
     * 
     */
    private String specific;

    /**
     * 
     */
    private String extra;

    /**
     * 
     */
    private String description;

    /**
     * 
     */
    private String uuid;

    /**
     * 
     */
    private String uid;

    /**
     * 
     */
    private String cr;


    @TableField(typeHandler = PGVectorTypeHandler.class)
    private float[] embedding;


    @TableField(exist = false)
    private double similarity;
}