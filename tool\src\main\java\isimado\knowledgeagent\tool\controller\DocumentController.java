package isimado.knowledgeagent.tool.controller;

import isimado.framework.base.Resp;
import isimado.knowledgeagent.tool.entity.dto.RagFlowResponse;
import isimado.knowledgeagent.tool.entity.dto.TextDTO;
import isimado.knowledgeagent.tool.service.DocumentRetrievalService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("document")
public class DocumentController {

    @Resource
    private DocumentRetrievalService retrievalService;



    @PostMapping("retrieval")
    public Resp retrieval(@RequestBody TextDTO dto) {
        RagFlowResponse flowResponse = retrievalService.retrievalDocument(dto.text());
        return Resp.success(flowResponse.data());
    }

}
