//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;


/**
 * http://www.sisostds.org/ontologies/smx#DISEntityType
 *
 * <p>DISEntityTypeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="DISEntityTypeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISCategory"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISCountry"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISDomain"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISExtra"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISKind"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISSpecific"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISSubCategory"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class DISEntityTypeType {

    @JsonProperty(value = "DISCategory")
    protected int disCategory;
    @JsonProperty(value = "DISCountry", required = true)
    protected int disCountry;
    @JsonProperty(value = "DISDomain")
    protected int disDomain;
    @JsonProperty(value = "DISExtra")
    protected int disExtra;
    @JsonProperty(value = "DISKind")
    protected int disKind;
    @JsonProperty(value = "DISSpecific")
    protected int disSpecific;
    @JsonProperty(value = "DISSubCategory")
    protected int disSubCategory;

    /**
     * 获取disCategory属性的值。
     */
    public int getDISCategory() {
        return disCategory;
    }

    /**
     * 设置disCategory属性的值。
     */
    public void setDISCategory(int value) {
        this.disCategory = value;
    }

    /**
     * 获取disCountry属性的值。
     *
     * @return possible object is
     * {@link int }
     */
    public int getDISCountry() {
        return disCountry;
    }

    /**
     * 设置disCountry属性的值。
     *
     * @param value allowed object is
     *              {@link BigInteger }
     */
    public void setDISCountry(int value) {
        this.disCountry = value;
    }

    /**
     * 获取disDomain属性的值。
     */
    public int getDISDomain() {
        return disDomain;
    }

    /**
     * 设置disDomain属性的值。
     */
    public void setDISDomain(int value) {
        this.disDomain = value;
    }

    /**
     * 获取disExtra属性的值。
     */
    public int getDISExtra() {
        return disExtra;
    }

    /**
     * 设置disExtra属性的值。
     */
    public void setDISExtra(int value) {
        this.disExtra = value;
    }

    /**
     * 获取disKind属性的值。
     */
    public int getDISKind() {
        return disKind;
    }

    /**
     * 设置disKind属性的值。
     */
    public void setDISKind(int value) {
        this.disKind = value;
    }

    /**
     * 获取disSpecific属性的值。
     */
    public int getDISSpecific() {
        return disSpecific;
    }

    /**
     * 设置disSpecific属性的值。
     */
    public void setDISSpecific(int value) {
        this.disSpecific = value;
    }

    /**
     * 获取disSubCategory属性的值。
     */
    public int getDISSubCategory() {
        return disSubCategory;
    }

    /**
     * 设置disSubCategory属性的值。
     */
    public void setDISSubCategory(int value) {
        this.disSubCategory = value;
    }

}
