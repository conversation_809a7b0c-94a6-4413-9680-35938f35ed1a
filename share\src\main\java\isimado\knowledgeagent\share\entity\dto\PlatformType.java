//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#Platform
 *
 * <p>PlatformType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PlatformType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Aircraft"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SubsurfaceVessel"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SurfaceVessel"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Vehicle"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PlatformType {

    @JsonProperty(value = "Aircraft")
    protected AircraftType aircraft;
    @JsonProperty(value = "SubsurfaceVessel")
    protected SubsurfaceVesselType subsurfaceVessel;
    @JsonProperty(value = "SurfaceVessel")
    protected SurfaceVesselType surfaceVessel;
    @JsonProperty(value = "Vehicle")
    protected VehicleType vehicle;

    /**
     * 获取aircraft属性的值。
     *
     * @return possible object is
     * {@link AircraftType }
     */
    public AircraftType getAircraft() {
        return aircraft;
    }

    /**
     * 设置aircraft属性的值。
     *
     * @param value allowed object is
     *              {@link AircraftType }
     */
    public void setAircraft(AircraftType value) {
        this.aircraft = value;
    }

    /**
     * 获取subsurfaceVessel属性的值。
     *
     * @return possible object is
     * {@link SubsurfaceVesselType }
     */
    public SubsurfaceVesselType getSubsurfaceVessel() {
        return subsurfaceVessel;
    }

    /**
     * 设置subsurfaceVessel属性的值。
     *
     * @param value allowed object is
     *              {@link SubsurfaceVesselType }
     */
    public void setSubsurfaceVessel(SubsurfaceVesselType value) {
        this.subsurfaceVessel = value;
    }

    /**
     * 获取surfaceVessel属性的值。
     *
     * @return possible object is
     * {@link SurfaceVesselType }
     */
    public SurfaceVesselType getSurfaceVessel() {
        return surfaceVessel;
    }

    /**
     * 设置surfaceVessel属性的值。
     *
     * @param value allowed object is
     *              {@link SurfaceVesselType }
     */
    public void setSurfaceVessel(SurfaceVesselType value) {
        this.surfaceVessel = value;
    }

    /**
     * 获取vehicle属性的值。
     *
     * @return possible object is
     * {@link VehicleType }
     */
    public VehicleType getVehicle() {
        return vehicle;
    }

    /**
     * 设置vehicle属性的值。
     *
     * @param value allowed object is
     *              {@link VehicleType }
     */
    public void setVehicle(VehicleType value) {
        this.vehicle = value;
    }

}
