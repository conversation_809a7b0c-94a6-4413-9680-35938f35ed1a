//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/smx#NameObservation
 *
 * <p>NameObservationType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="NameObservationType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ObservationGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}HostilityStatusCode" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Marking" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Name"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Side" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class NameObservationType {

    @JsonProperty(value = "ActorReference")
    protected String actorReference;
    @JsonProperty(value = "ConfidenceLevel")
    protected Double confidenceLevel;
    @JsonProperty(value = "UncertaintyInterval")
    protected Double uncertaintyInterval;
    @JsonProperty(value = "HostilityStatusCode")

    protected HostilityStatusCodeType hostilityStatusCode;
    @JsonProperty(value = "Marking")
    protected String marking;
    @JsonProperty(value = "Name", required = true)
    protected String name;
    @JsonProperty(value = "Side")
    protected String side;

    /**
     * 获取actorReference属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getActorReference() {
        return actorReference;
    }

    /**
     * 设置actorReference属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setActorReference(String value) {
        this.actorReference = value;
    }

    /**
     * 获取confidenceLevel属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getConfidenceLevel() {
        return confidenceLevel;
    }

    /**
     * 设置confidenceLevel属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setConfidenceLevel(Double value) {
        this.confidenceLevel = value;
    }

    /**
     * 获取uncertaintyInterval属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getUncertaintyInterval() {
        return uncertaintyInterval;
    }

    /**
     * 设置uncertaintyInterval属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setUncertaintyInterval(Double value) {
        this.uncertaintyInterval = value;
    }

    /**
     * 获取hostilityStatusCode属性的值。
     *
     * @return possible object is
     * {@link HostilityStatusCodeType }
     */
    public HostilityStatusCodeType getHostilityStatusCode() {
        return hostilityStatusCode;
    }

    /**
     * 设置hostilityStatusCode属性的值。
     *
     * @param value allowed object is
     *              {@link HostilityStatusCodeType }
     */
    public void setHostilityStatusCode(HostilityStatusCodeType value) {
        this.hostilityStatusCode = value;
    }

    /**
     * 获取marking属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMarking() {
        return marking;
    }

    /**
     * 设置marking属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMarking(String value) {
        this.marking = value;
    }

    /**
     * 获取name属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * 获取side属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSide() {
        return side;
    }

    /**
     * 设置side属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSide(String value) {
        this.side = value;
    }

}
