//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.07.05 时间 04:23:24 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/lox#PlanPhase
 *
 * <p>PlanPhaseType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PlanPhaseType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhaseCompletionCondition"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhaseTrigger"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SubPhase" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskReference" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}UUID"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Name" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PlanPhaseType {

    @JsonProperty(value = "PlanPhaseCompletionCondition", required = true)

    protected PlanPhaseCompletionConditionType planPhaseCompletionCondition;
    @JsonProperty(value = "PlanPhaseTrigger", required = true)
    protected PlanPhaseTriggerType planPhaseTrigger;
    @JsonProperty(value = "SubPhase")
    protected List<PlanPhaseType> subPhase;
    @JsonProperty(value = "TaskReference")
    protected List<String> taskReference;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "Sort")
    protected Integer sort;

    /**
     * 获取planPhaseCompletionCondition属性的值。
     *
     * @return possible object is
     * {@link PlanPhaseCompletionConditionType }
     */
    public PlanPhaseCompletionConditionType getPlanPhaseCompletionCondition() {
        return planPhaseCompletionCondition;
    }

    /**
     * 设置planPhaseCompletionCondition属性的值。
     *
     * @param value allowed object is
     *              {@link PlanPhaseCompletionConditionType }
     */
    public void setPlanPhaseCompletionCondition(PlanPhaseCompletionConditionType value) {
        this.planPhaseCompletionCondition = value;
    }

    /**
     * 获取planPhaseTrigger属性的值。
     *
     * @return possible object is
     * {@link PlanPhaseTriggerType }
     */
    public PlanPhaseTriggerType getPlanPhaseTrigger() {
        return planPhaseTrigger;
    }

    /**
     * 设置planPhaseTrigger属性的值。
     *
     * @param value allowed object is
     *              {@link PlanPhaseTriggerType }
     */
    public void setPlanPhaseTrigger(PlanPhaseTriggerType value) {
        this.planPhaseTrigger = value;
    }

    /**
     * Gets the value of the subPhase property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the subPhase property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSubPhase().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PlanPhaseType }
     */
    public List<PlanPhaseType> getSubPhase() {
        if (subPhase == null) {
            subPhase = new ArrayList<PlanPhaseType>();
        }
        return this.subPhase;
    }

    /**
     * Gets the value of the taskReference property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taskReference property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaskReference().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getTaskReference() {
        if (taskReference == null) {
            taskReference = new ArrayList<String>();
        }
        return this.taskReference;
    }

    /**
     * 获取uuid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getUUID() {
        return uuid;
    }

    /**
     * 设置uuid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUUID(String value) {
        this.uuid = value;
    }

    /**
     * 获取name属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
