<?xml version='1.0' encoding='utf-8'?>
<objectModel 
  xmlns="http://standards.ieee.org/IEEE1516-2010" 
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://standards.ieee.org/IEEE1516-2010 http://standards.ieee.org/downloads/1516/1516.2-2010/IEEE1516-DIF-2010.xsd">
  <modelIdentification>
    <name>JSimHMI</name>
    <type>FOM</type>
    <version>1.0</version>
    <reference>
      <type>Dependency</type>
      <identification>JSimBasic</identification>
    </reference>
  </modelIdentification>
  <objects>
    <objectClass>
      <name>HLAobjectRoot</name>
      <objectClass>
        <name>HMI</name>
        <sharing>Publish</sharing>
        <objectClass>
          <name>ForceHMI</name>
          <sharing>Publish</sharing>
          <attribute>
            <name>HMIObjectList</name>
            <dataType>MorpheusString</dataType>
            <updateType>Conditional</updateType>
            <updateCondition>On Change</updateCondition>
            <ownership>Divest</ownership>
            <sharing>PublishSubscribe</sharing>
            <transportation>HLAbestEffort</transportation>
            <order>Receive</order>
            <semantics>N/A</semantics>
          </attribute>
        </objectClass>
      </objectClass>
      <objectClass>
        <name>Assess</name>
        <sharing>Publish</sharing>
        <semantics>评估模型基类</semantics>
        <attribute>
          <name>Name</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>名称</semantics>
        </attribute>
        <attribute>
          <name>ForceNum</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>各属方不同平台的数量</semantics>
        </attribute>
        <attribute>
          <name>PlatformDamageInfo</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>平台战损信息</semantics>
        </attribute>
        <attribute>
          <name>WeaponCountAndDamageInfo</name>
          <dataType>MorpheusString</dataType>
          <updateType>Conditional</updateType>
          <updateCondition>On Change</updateCondition>
          <ownership>Divest</ownership>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAbestEffort</transportation>
          <order>Receive</order>
          <semantics>武器发射数量以及战损信息</semantics>
        </attribute>
      </objectClass>
    </objectClass>
  </objects>
  <interactions>
    <interactionClass>
      <name>HLAinteractionRoot</name>
      <interactionClass>
        <name>CommandHMI</name>
        <sharing>PublishSubscribe</sharing>
        <transportation>HLAreliable</transportation>
        <order>Receive</order>
        <interactionClass>
          <name>B10001</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇定速定向</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10002</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇计划航线</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10003</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇区域机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10004</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇伴随机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>followPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标批号</semantics>
          </parameter>
          <parameter>
            <name>followDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>相对距离（海里）</semantics>
          </parameter>
          <parameter>
            <name>followAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>方位角</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10005</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对空侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10006</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对海侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10007</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对潜侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位(区域类型为矩形时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10008</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对空抗击</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>打击模式</semantics>
          </parameter>
          <parameter>
            <name>pointDefense</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>点防御标识, true为点防御, false为编队协同的面防御(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>salvoes</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>齐射数量, 如1拦1, 2拦1(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelStart</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的开始角度, 正北为0度, 顺时针旋转, 默认0(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelEnd</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的结束角度, 默认360(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>发射的武器名称（可选参数</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>weaponRules</name>
            <dataType>WeaponRuleArray</dataType>
            <semantics>设置武器规则（可选参数)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10009</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10010</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对陆攻击</semantics>
          <order>Receive</order>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10011</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇对潜攻击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10012</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇红外防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10013</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇箔条防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10014</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇雷达干扰</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标(瞄准式下参数)</semantics>
          </parameter>
          <parameter>
            <name>techniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰方式(0-杂波干扰)</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型(0-范围干扰 1-瞄准式)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10015</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>水面舰艇通信干扰</semantics>
          <parameter>
            <name>jammingTarget</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标id(瞄准式干扰下参数)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型 0-范围干扰 1-瞄准式干扰</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10016</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇布雷</semantics>
          <order>Receive</order>
          <parameter>
            <name>mineName</name>
            <dataType>HLAargumentList</dataType>
            <semantics>水雷名称</semantics>
          </parameter>
          <parameter>
            <name>area</name>
            <dataType>Waypoints</dataType>
            <semantics>雷场区域</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10017</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇扫雷</semantics>
          <order>Receive</order>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10018</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇装载</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被装载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10019</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇卸载</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被卸载单元ID</semantics>
          </parameter>
          <parameter>
            <name>fullUnloading</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>是否全部卸载</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10020</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇补给</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>补给船实体ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10021</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇修理</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>被修理平台实体ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10022</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇拖带</semantics>
          <order>Receive</order>
          <parameter>
            <name>tractionId</name>
            <dataType>MorpheusString</dataType>
            <semantics>被拖带平台ID</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>拖带机动路线</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10023</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇进港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10024</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇出港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10025</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>水面舰艇指挥权变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10026</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>altitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10027</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵计划航线</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10028</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10029</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵伴随机动</semantics>
          <parameter>
            <name>followPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标批号</semantics>
          </parameter>
          <parameter>
            <name>followDistance</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>相对距离(m)</semantics>
          </parameter>
          <parameter>
            <name>followAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>方位角(度)</semantics>
          </parameter>
          <parameter>
            <name>followHeight</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>相对高度(m)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10030</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对空侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10031</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对海侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10033</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空战</semantics>
          <parameter>
            <name>airWarfareTargetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵空战目标id</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10034</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵拦截</semantics>
          <parameter>
            <name>airWarfareTargetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>航空兵拦截目标id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10035</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对舰攻击</semantics>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10036</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对陆攻击</semantics>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10037</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对潜攻击</semantics>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标id</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划航线(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10042</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵布雷</semantics>
          <parameter>
            <name>mineName</name>
            <dataType>HLAargumentList</dataType>
            <semantics>水雷名称</semantics>
          </parameter>
          <parameter>
            <name>area</name>
            <dataType>Waypoints</dataType>
            <semantics>雷场区域</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10043</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵起飞</semantics>
          <parameter>
            <name>launchAircraftId</name>
            <dataType>MorpheusString</dataType>
            <semantics>起飞的飞机名称(型号)</semantics>
          </parameter>
          <parameter>
            <name>nums</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>起飞的飞机数量</semantics>
          </parameter>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>批号</semantics>
          </parameter>
          <parameter>
            <name>loadoutID</name>
            <dataType>MorpheusString</dataType>
            <semantics>挂载方案</semantics>
          </parameter>
          <parameter>
            <name>tgtHeading</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>飞机航向</semantics>
          </parameter>
          <parameter>
            <name>tgtSpeed</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>飞机速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>tgtHeight</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>飞机高度(m)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10044</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵降落</semantics>
          <parameter>
            <name>landPlatformId</name>
            <dataType>MorpheusString</dataType>
            <semantics>降落的目标平台/机场ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10047</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵转场</semantics>
          <parameter>
            <name>launchAircraftId</name>
            <dataType>MorpheusString</dataType>
            <semantics>飞机名称(型号)</semantics>
          </parameter>
          <parameter>
            <name>nums</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>转场飞机数量</semantics>
          </parameter>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>批号</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>转场的目标平台/机场ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10048</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空中加油</semantics>
          <parameter>
            <name>refuelPlatform</name>
            <dataType>MorpheusString</dataType>
            <semantics>加油平台ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10049</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵装载</semantics>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被装载单元ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10050</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵卸载</semantics>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被卸载单元ID</semantics>
          </parameter>
          <parameter>
            <name>fullUnloading</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>是否全部卸载</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10051</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵空投</semantics>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被空投单元ID</semantics>
          </parameter>
          <parameter>
            <name>fullUnloading</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>是否全部投放</semantics>
          </parameter>
          <parameter>
            <name>areaPoInts</name>
            <dataType>Waypoints</dataType>
            <semantics>空投区域点位</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10052</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵指挥权变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10039</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵雷达干扰</semantics>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标(瞄准式下参数)</semantics>
          </parameter>
          <parameter>
            <name>techniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰方式(0-杂波干扰)</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型(0-范围干扰 1-瞄准式)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10040</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵通信干扰</semantics>
          <parameter>
            <name>jammingTarget</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标id(瞄准式干扰下参数)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型 0-范围干扰 1-瞄准式干扰</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10041</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵发射诱饵弹</semantics>
          <parameter>
            <name>simulateName</name>
            <dataType>MorpheusString</dataType>
            <semantics>模拟目标类型</semantics>
          </parameter>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>诱饵弹批号</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>发射方位(手动模式参数)(度，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10038</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵红外防护</semantics>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>攻击模式(自动-Auto 手动-Manual)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>发射方位(手动模式参数)(度，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10110</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵部署箔条走廊</semantics>
          <parameter>
            <name>deploymentLength</name>
            <dataType>HLAfloat32BE</dataType>
            <semantics>部署长度(m)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10032</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵对潜搜索</semantics>
          <parameter>
            <name>buoyName</name>
            <dataType>MorpheusString</dataType>
            <semantics>声纳浮标型号（投放声呐浮标时必选）</semantics>
          </parameter>
          <parameter>
            <name>searchType</name>
            <dataType>SearchTypeEnum</dataType>
            <semantics>搜索类型(1-直升机吊放声呐 2-投放声呐浮标)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>NoAltitudeWaypointArray</dataType>
            <semantics>声呐吊放点/浮标投放点</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10045</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵等级转进</semantics>
          <parameter>
            <name>equipType</name>
            <dataType>MorpheusString</dataType>
            <semantics>飞机名称(型号)</semantics>
          </parameter>
          <parameter>
            <name>equipNums</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>飞机数量</semantics>
          </parameter>
          <parameter>
            <name>loadoutID</name>
            <dataType>MorpheusString</dataType>
            <semantics>挂载方案(等级3转2时所需参数)</semantics>
          </parameter>
          <parameter>
            <name>combatReadyLevel</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>转换等级</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20052</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵悬停</semantics>
        </interactionClass>
        <interactionClass>
          <name>B20053</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵返航</semantics>
        </interactionClass>
        <interactionClass>
          <name>B10053</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>altitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10054</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇计划航线</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10055</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10056</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇对潜搜索</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10057</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇对海侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10058</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10059</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对地打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10060</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇对潜打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10062</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇充电</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>chargePercent</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>充电百分比</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10063</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇投放</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线(非必填)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>UnitIds</dataType>
            <semantics>被投放单元ID</semantics>
          </parameter>
          <parameter>
            <name>fullUnloading</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>是否全部投放</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10064</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇进港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10065</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>潜艇出港</semantics>
          <order>Receive</order>
          <parameter>
            <name>portId</name>
            <dataType>MorpheusString</dataType>
            <semantics>海港ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10066</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇指挥权变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10061</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>潜艇水声防护</semantics>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>模式（自动Auto,手动模式Manual）</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10082</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>编队聚合实体区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>HLAargumentList</dataType>
            <semantics>编队成员</semantics>
          </parameter>
          <parameter>
            <name>formationConfig</name>
            <dataType>FormationConfig</dataType>
            <semantics>编队阵型配置</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10080</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>编队聚合实体定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>altitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>朝向（度数）</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>HLAargumentList</dataType>
            <semantics>编队成员</semantics>
          </parameter>
          <parameter>
            <name>formationConfig</name>
            <dataType>FormationConfig</dataType>
            <semantics>编队阵型配置</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10081</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>编队聚合实体计划航线</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路线</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>unitIds</name>
            <dataType>HLAargumentList</dataType>
            <semantics>编队成员</semantics>
          </parameter>
          <parameter>
            <name>formationConfig</name>
            <dataType>FormationConfig</dataType>
            <semantics>编队阵型配置</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10083</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>聚合</semantics>
          <parameter>
            <name>batchIdList</name>
            <dataType>HLAargumentList</dataType>
            <semantics>聚合的实体批号列表</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10084</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>解聚</semantics>
          <parameter>
            <name>batchIdList</name>
            <dataType>HLAargumentList</dataType>
            <semantics>解聚批号列表</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10201</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>聚合实体巡逻</semantics>
          <parameter>
            <name>route</name>
            <dataType>Waypoints</dataType>
            <semantics>路线</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10202</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>聚合实体通信干扰防护</semantics>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20030</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>水面舰艇编队防空</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>pointDefense</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>点防御标识, true为点防御(360度防御), false为编队协同的面防御</semantics>
          </parameter>
          <parameter>
            <name>maxDistanceToCommander</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>距离指挥官的最大距离，超过此距离后无法参与编队协同防空。默认值100公里</semantics>
          </parameter>
          <parameter>
            <name>containsCommanderFlag</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>防空指令里是否包含指挥官本身，默认不包含</semantics>
          </parameter>
          <parameter>
            <name>sectorOverlapAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>编队成员防御扇面边界交叉角度(默认5度)</semantics>
          </parameter>
          <parameter>
            <name>groupAngelDefenceConf</name>
            <dataType>GroupAngelDefenceModeArray</dataType>
            <semantics>为指定的编队成员分配防御角度，未指定则根据相对位置平均分配</semantics>
          </parameter>
          <parameter>
            <name>salvoes</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>齐射数量, 如1拦1, 2拦1(默认2)</semantics>
          </parameter>
          <parameter>
            <name>weaponRules</name>
            <dataType>WeaponRuleArray</dataType>
            <semantics>设置武器规则（可选参数)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10085</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>航空兵编队队形变换</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路线</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>scale</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>缩放比例</semantics>
          </parameter>
          <parameter>
            <name>formationConfig</name>
            <dataType>FormationConfig</dataType>
            <semantics>编队阵型配置</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10067</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队定速定向</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10068</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队路线机动</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10069</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队进攻</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>进攻目标</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10070</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队防御</semantics>
          <order>Receive</order>
          <parameter>
            <name>defendStrategy</name>
            <dataType>RulesOfEngagementEnum8</dataType>
            <semantics>防御策略，0不防御，1遇敌防御反击，2遭敌进攻反击</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10073</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队开辟通路</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>openWayType</name>
            <dataType>OpenWayEnum</dataType>
            <semantics>通路枚举，1表示坦克通路，2表示舰艇通路，3表示步兵通路</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10072</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队机降</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10071</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>陆战分队泛水</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路线</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10075</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>陆战分队指挥权变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10095</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>编队聚合实体指挥关系变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10088</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群队形变换</semantics>
          <parameter>
            <name>aggFormation</name>
            <dataType>AggregateStateFormationEnum32</dataType>
            <semantics>队形</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B90011</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群定速定向</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>altitude</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B90010</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群计划航线</semantics>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B90012</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群区域机动</semantics>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10093</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群对海侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10094</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群对潜搜索</semantics>
          <parameter>
            <name>buoyName</name>
            <dataType>MorpheusString</dataType>
            <semantics>声纳浮标型号（投放声呐浮标时必选）</semantics>
          </parameter>
          <parameter>
            <name>searchType</name>
            <dataType>SearchTypeEnum</dataType>
            <semantics>搜索类型(1-直升机吊放声呐 2-投放声呐浮标)</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>NoAltitudeWaypointArray</dataType>
            <semantics>声呐吊放点/浮标投放点</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10092</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群对空侦察</semantics>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>height</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>高度(m)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10091</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群通信干扰</semantics>
          <parameter>
            <name>jammingTarget</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标id(瞄准式干扰下参数)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型 0-范围干扰 1-瞄准式干扰</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10090</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>蜂群雷达干扰</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标(瞄准式下参数)</semantics>
          </parameter>
          <parameter>
            <name>techniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰方式(0-杂波干扰)</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型(0-范围干扰 1-瞄准式)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10089</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>蜂群火力运用</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>武器名称</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>武器数量</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20020</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>编队火力运用</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>武器名称</semantics>
          </parameter>
          <parameter>
            <name>weaponNum</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>武器数量</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20010</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>航空编队火力运用</semantics>
          <order>Receive</order>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20032</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>激光干扰</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标</semantics>
          </parameter>
          <parameter>
            <name>jammingDuration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰时长（单位秒）</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10074</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>陆战分队释放烟雾</semantics>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射角度</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20037</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>传感器开关机</semantics>
          <parameter>
            <name>turnOn</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>开关机标识(1开机，0关机)</semantics>
          </parameter>
          <parameter>
            <name>attribute</name>
            <dataType>MorpheusString</dataType>
            <semantics>工作参数</semantics>
          </parameter>
          <parameter>
            <name>name</name>
            <dataType>MorpheusString</dataType>
            <semantics>需要开关机的传感器名称(型号)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20038</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>水面舰船/潜艇出港</semantics>
          <parameter>
            <name>shipList</name>
            <dataType>HLAargumentList</dataType>
            <semantics>出港舰船/潜艇id列表</semantics>
          </parameter>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>批号</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10096</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台定速定向</semantics>
          <order>Receive</order>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>heading</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>航向(度)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10097</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台计划航线</semantics>
          <order>Receive</order>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>路径点</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum</dataType>
            <semantics>机动完成后行为(0:保持当前航向机动；1：停止；2:循环)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10098</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对空侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10099</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对海侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10100</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对潜侦察</semantics>
          <order>Receive</order>
          <parameter>
            <name>deviceName</name>
            <dataType>MorpheusString</dataType>
            <semantics>侦察设备名称</semantics>
          </parameter>
          <parameter>
            <name>speed</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>速度(m/s)</semantics>
          </parameter>
          <parameter>
            <name>areaPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>区域范围点位</semantics>
          </parameter>
          <parameter>
            <name>arrivedAction</name>
            <dataType>ArrivedActionEnum2</dataType>
            <semantics>机动完成后行为(0:循环；1：停止)</semantics>
          </parameter>
          <parameter>
            <name>maneuverMode</name>
            <dataType>ManeuverModeEnum</dataType>
            <semantics>机动模式(1-8字型 2-跑道型 3-z字型)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10101</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对空打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>打击模式</semantics>
          </parameter>
          <parameter>
            <name>pointDefense</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>点防御标识, true为点防御, false为编队协同的面防御(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>salvoes</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>齐射数量, 如1拦1, 2拦1(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelStart</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的开始角度, 正北为0度, 顺时针旋转, 默认0(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelEnd</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的结束角度, 默认360(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>发射的武器名称（可选参数</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10102</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10103</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台对地打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10104</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台红外防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10105</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台箔条防护</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>自动Auto,手动模式Manual</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID(手动模式参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>fireAngel</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>发射方位(手动模式参数，正北为零，顺时针为正方向)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10106</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台雷达干扰</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标(瞄准式下参数)</semantics>
          </parameter>
          <parameter>
            <name>techniqueId</name>
            <dataType>TechniqueEnum</dataType>
            <semantics>干扰方式(0-杂波干扰)</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型(0-范围干扰 1-瞄准式)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10107</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>地面平台通信干扰</semantics>
          <parameter>
            <name>jammingTarget</name>
            <dataType>MorpheusString</dataType>
            <semantics>干扰目标id(瞄准式干扰下参数)</semantics>
          </parameter>
          <parameter>
            <name>jammingType</name>
            <dataType>JammerEnum</dataType>
            <semantics>干扰类型 0-范围干扰 1-瞄准式干扰</semantics>
          </parameter>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10108</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>地面平台修理</semantics>
          <order>Receive</order>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>被修理平台实体ID</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10109</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>地面平台指挥权变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10076</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>岸导对空打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>attackMode</name>
            <dataType>AttackModeEnum</dataType>
            <semantics>打击模式</semantics>
          </parameter>
          <parameter>
            <name>pointDefense</name>
            <dataType>MorpheusBoolean</dataType>
            <semantics>点防御标识, true为点防御, false为编队协同的面防御(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>salvoes</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>齐射数量, 如1拦1, 2拦1(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelStart</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的开始角度, 正北为0度, 顺时针旋转, 默认0(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>attackAreaAngelEnd</name>
            <dataType>HLAfloat64BE</dataType>
            <semantics>面防御的结束角度, 默认360(自动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量(手动模式下所需参数)</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>发射的武器名称（可选参数</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10077</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>岸导对海打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>capType</name>
            <dataType>TargetCaptureModeEnum</dataType>
            <semantics>目标捕获模式(0：大目标(默认)；1：小目标；2：随机；4：方向左；5：方向中；6：方向右)</semantics>
          </parameter>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标Id</semantics>
          </parameter>
          <parameter>
            <name>targetPoint</name>
            <dataType>LocationLLA</dataType>
            <semantics>目标点</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10078</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <semantics>岸导对地打击</semantics>
          <order>Receive</order>
          <parameter>
            <name>fireCount</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>发射数量</semantics>
          </parameter>
          <parameter>
            <name>weaponName</name>
            <dataType>MorpheusString</dataType>
            <semantics>选择的武器名称(可选参数，不指定时自动查找可用武器)</semantics>
          </parameter>
          <parameter>
            <name>targetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标ID</semantics>
          </parameter>
          <parameter>
            <name>customCruiseWayPoints</name>
            <dataType>Waypoints</dataType>
            <semantics>规划导弹航路(可选参数)</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B10079</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>岸导指挥关系变更</semantics>
          <parameter>
            <name>exchangeStation</name>
            <dataType>MorpheusString</dataType>
            <semantics>请求编队id</semantics>
          </parameter>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B30001</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>通信干扰防护</semantics>
          <parameter>
            <name>duration</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>干扰持续时间(分钟)</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20031</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>蜂群撞击</semantics>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>TargetId</name>
            <dataType>MorpheusString</dataType>
            <semantics>目标</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20042</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>分批</semantics>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>新批号</semantics>
          </parameter>
          <parameter>
            <name>entityList</name>
            <dataType>HLAargumentList</dataType>
            <semantics>分批实体列表</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20043</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>合批</semantics>
          <parameter>
            <name>batchID</name>
            <dataType>MorpheusString</dataType>
            <semantics>待合入批号</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20035</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>投放无人机</semantics>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>name</name>
            <dataType>MorpheusString</dataType>
            <semantics>无人机型号（名称）</semantics>
          </parameter>
          <parameter>
            <name>count</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>投放数量</semantics>
          </parameter>
          <parameter>
            <name>launchPeriod</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>投放间隔（单位秒）</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>投放路线</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20036</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>回收无人机</semantics>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
        </interactionClass>
        <interactionClass>
          <name>B20054</name>
          <sharing>PublishSubscribe</sharing>
          <transportation>HLAreliable</transportation>
          <order>Receive</order>
          <semantics>投放空飘球</semantics>
          <parameter>
            <name>startTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>开始时间</semantics>
          </parameter>
          <parameter>
            <name>endTime</name>
            <dataType>MorpheusString</dataType>
            <semantics>结束时间</semantics>
          </parameter>
          <parameter>
            <name>name</name>
            <dataType>MorpheusString</dataType>
            <semantics>空飘球（名称）</semantics>
          </parameter>
          <parameter>
            <name>count</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>投放数量</semantics>
          </parameter>
          <parameter>
            <name>launchPeriod</name>
            <dataType>HLAinteger32BE</dataType>
            <semantics>投放间隔（单位秒）</semantics>
          </parameter>
          <parameter>
            <name>route</name>
            <dataType>WaypointArray</dataType>
            <semantics>投放路线</semantics>
          </parameter>
        </interactionClass>
      </interactionClass>
    </interactionClass>
  </interactions>
  <dimensions />
  <time>
    <timeStamp>
      <dataType>HLAinteger64Time</dataType>
      <semantics>Number of microseconds since the Epoch. The default Epoch is 00:00 Jan 1, 1970 in the UTC+0 time
                zone, but other Epochs may be agreed by a federation. Leap days are included in the calculation, but
                leap seconds are not.</semantics>
    </timeStamp>
    <lookahead>
      <dataType>HLAinteger64Time</dataType>
      <semantics>Number or microseconds.</semantics>
    </lookahead>
  </time>
  <tags>
    <updateReflectTag>
      <dataType>RPRUserDefinedTag</dataType>
      <semantics>User-supplied tag provided with each update/reflect of object instance attribute values. Contains
                (at least) the time at which the provided data is valid.</semantics>
    </updateReflectTag>
    <sendReceiveTag>
      <dataType>RPRUserDefinedTag</dataType>
      <semantics>User-supplied tag provided with each send/receive of an interaction. Contains (at least) the time
                at which the provided data is valid.</semantics>
    </sendReceiveTag>
    <deleteRemoveTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </deleteRemoveTag>
    <divestitureRequestTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </divestitureRequestTag>
    <divestitureCompletionTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </divestitureCompletionTag>
    <acquisitionRequestTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </acquisitionRequestTag>
    <requestUpdateTag>
      <dataType>NA</dataType>
      <semantics>NA</semantics>
    </requestUpdateTag>
  </tags>
  <synchronizations>
    <synchronizationPoint>
      <label>RPRobjectsRegistered</label>
      <dataType>NA</dataType>
      <capability>NA</capability>
      <semantics>Indicates that all object instances have been registered.</semantics>
    </synchronizationPoint>
    <synchronizationPoint>
      <label>RPRattributesUpdated</label>
      <dataType>NA</dataType>
      <capability>NA</capability>
      <semantics>Indicates that all required updates with initial values have been provided for all registered
                instances.</semantics>
    </synchronizationPoint>
  </synchronizations>
  <transportations />
  <updateRates />
  <dataTypes>
    <basicDataRepresentations />
    <simpleDataTypes />
    <enumeratedDataTypes>
      <enumeratedData>
        <name>ArrivedActionEnum</name>
        <representation>HLAoctet</representation>
        <semantics>机动完成后行为枚举</semantics>
        <enumerator>
          <name>KEEP</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>STOP</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>CIRCULATION</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ArrivedActionEnum2</name>
        <representation>HLAoctet</representation>
        <semantics>区域机动完成后行为枚举</semantics>
        <enumerator>
          <name>PATROL</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>STOP</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ManeuverModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>机动模式枚举</semantics>
        <enumerator>
          <name>EightShaped</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>RunwayType</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>ZShaped</name>
          <value>3</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>AreaTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>区域类型枚举</semantics>
        <enumerator>
          <name>Rectangle</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Circular</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>AttackModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>攻击模式枚举</semantics>
        <enumerator>
          <name>Auto</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>Manual</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>MaterialTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>补给类型枚举</semantics>
        <enumerator>
          <name>Weapon</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Fuel</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>RepairTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>修理类型枚举</semantics>
        <enumerator>
          <name>Fire</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Flooding</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ReturnModeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>返航方式枚举</semantics>
        <enumerator>
          <name>FH</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>ZDDWFH</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>ExchangeRequestEnum</name>
        <representation>HLAoctet</representation>
        <semantics>变更指挥请求类型枚举</semantics>
        <enumerator>
          <name>BecomeSubordinate</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>BecomeCommander</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>TechniqueEnum</name>
        <representation>HLAoctet</representation>
        <semantics>干扰类型枚举</semantics>
        <enumerator>
          <name>ZBGR</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>JMBGR</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>JammerEnum</name>
        <representation>HLAoctet</representation>
        <semantics>干扰类型枚举</semantics>
        <enumerator>
          <name>FWGR</name>
          <value>0</value>
        </enumerator>
        <enumerator>
          <name>MZGR</name>
          <value>1</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>AirWarfareTacticsEnum</name>
        <representation>HLAoctet</representation>
        <semantics>空战策略枚举</semantics>
        <enumerator>
          <name>DJCL</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>LDGRCL</name>
          <value>2</value>
        </enumerator>
        <enumerator>
          <name>TXGRCL</name>
          <value>3</value>
        </enumerator>
      </enumeratedData>
      <enumeratedData>
        <name>SearchTypeEnum</name>
        <representation>HLAoctet</representation>
        <semantics>航空兵对潜搜索类型枚举</semantics>
        <enumerator>
          <name>Hang</name>
          <value>1</value>
        </enumerator>
        <enumerator>
          <name>Buoy</name>
          <value>2</value>
        </enumerator>
      </enumeratedData>
    </enumeratedDataTypes>
    <arrayDataTypes>
      <arrayData>
        <name>UnitIds</name>
        <dataType>MorpheusString</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>N/A</semantics>
      </arrayData>
      <arrayData>
        <name>NoneAltitudeWaypoints</name>
        <dataType>NoneAltitudeLocation</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>N/A</semantics>
      </arrayData>
      <arrayData>
        <name>NoAltitudeWaypointArray</name>
        <dataType>NoAltitudeWaypoint</dataType>
        <cardinality>Dynamic</cardinality>
        <encoding>HLAvariableArray</encoding>
        <semantics>无高度的路径点列表</semantics>
      </arrayData>
    </arrayDataTypes>
    <fixedRecordDataTypes>
      <fixedRecordData>
        <name>HMIObject</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
      </fixedRecordData>
      <fixedRecordData>
        <name>NoneAltitudeLocation</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>N/A</semantics>
        <field>
          <name>Latitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>纬度</semantics>
        </field>
        <field>
          <name>Longitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>经度</semantics>
        </field>
      </fixedRecordData>
      <fixedRecordData>
        <name>NoAltitudeWaypoint</name>
        <encoding>HLAfixedRecord</encoding>
        <semantics>无高度的路径点</semantics>
        <field>
          <name>Latitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>纬度</semantics>
        </field>
        <field>
          <name>Longitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>经度</semantics>
        </field>
        <field>
          <name>Altitude</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>高度(m)</semantics>
        </field>
        <field>
          <name>Speed</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>速度(m/s)</semantics>
        </field>
        <field>
          <name>StayTime</name>
          <dataType>HLAfloat64BE</dataType>
          <semantics>停留时间</semantics>
        </field>
      </fixedRecordData>
    </fixedRecordDataTypes>
    <variantRecordDataTypes />
  </dataTypes>
  <notes />
</objectModel>