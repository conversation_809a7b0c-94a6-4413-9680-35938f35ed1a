//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>SystemCommandTypeCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="SystemCommandTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="InitializationComplete"/>
 *     &lt;enumeration value="ShareScenario"/>
 *     &lt;enumeration value="StartScenario"/>
 *     &lt;enumeration value="StopScenario"/>
 *     &lt;enumeration value="SubmitInitialization"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum SystemCommandTypeCodeType {

    INITIALIZATION_COMPLETE("InitializationComplete"),
    SHARE_SCENARIO("ShareScenario"),
    START_SCENARIO("StartScenario"),
    STOP_SCENARIO("StopScenario"),
    SUBMIT_INITIALIZATION("SubmitInitialization");
    private final String value;

    SystemCommandTypeCodeType(String v) {
        value = v;
    }

    public static SystemCommandTypeCodeType fromValue(String v) {
        for (SystemCommandTypeCodeType c : SystemCommandTypeCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
