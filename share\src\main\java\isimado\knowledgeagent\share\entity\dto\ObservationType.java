//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/smx#Observation
 *
 * <p>ObservationType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ObservationType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActivityObservation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}HealthObservation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}LocationObservation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}NameObservation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ResourceObservation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SubjectTypeObservation"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class ObservationType {

    @JsonProperty(value = "ActivityObservation")
    protected ActivityObservationType activityObservation;
    @JsonProperty(value = "HealthObservation")
    protected HealthObservationType healthObservation;
    @JsonProperty(value = "LocationObservation")
    protected LocationObservationType locationObservation;
    @JsonProperty(value = "NameObservation")
    protected NameObservationType nameObservation;
    @JsonProperty(value = "ResourceObservation")
    protected ResourceObservationType resourceObservation;
    @JsonProperty(value = "SubjectTypeObservation")
    protected SubjectTypeObservationType subjectTypeObservation;

    /**
     * 获取activityObservation属性的值。
     *
     * @return possible object is
     * {@link ActivityObservationType }
     */
    public ActivityObservationType getActivityObservation() {
        return activityObservation;
    }

    /**
     * 设置activityObservation属性的值。
     *
     * @param value allowed object is
     *              {@link ActivityObservationType }
     */
    public void setActivityObservation(ActivityObservationType value) {
        this.activityObservation = value;
    }

    /**
     * 获取healthObservation属性的值。
     *
     * @return possible object is
     * {@link HealthObservationType }
     */
    public HealthObservationType getHealthObservation() {
        return healthObservation;
    }

    /**
     * 设置healthObservation属性的值。
     *
     * @param value allowed object is
     *              {@link HealthObservationType }
     */
    public void setHealthObservation(HealthObservationType value) {
        this.healthObservation = value;
    }

    /**
     * 获取locationObservation属性的值。
     *
     * @return possible object is
     * {@link LocationObservationType }
     */
    public LocationObservationType getLocationObservation() {
        return locationObservation;
    }

    /**
     * 设置locationObservation属性的值。
     *
     * @param value allowed object is
     *              {@link LocationObservationType }
     */
    public void setLocationObservation(LocationObservationType value) {
        this.locationObservation = value;
    }

    /**
     * 获取nameObservation属性的值。
     *
     * @return possible object is
     * {@link NameObservationType }
     */
    public NameObservationType getNameObservation() {
        return nameObservation;
    }

    /**
     * 设置nameObservation属性的值。
     *
     * @param value allowed object is
     *              {@link NameObservationType }
     */
    public void setNameObservation(NameObservationType value) {
        this.nameObservation = value;
    }

    /**
     * 获取resourceObservation属性的值。
     *
     * @return possible object is
     * {@link ResourceObservationType }
     */
    public ResourceObservationType getResourceObservation() {
        return resourceObservation;
    }

    /**
     * 设置resourceObservation属性的值。
     *
     * @param value allowed object is
     *              {@link ResourceObservationType }
     */
    public void setResourceObservation(ResourceObservationType value) {
        this.resourceObservation = value;
    }

    /**
     * 获取subjectTypeObservation属性的值。
     *
     * @return possible object is
     * {@link SubjectTypeObservationType }
     */
    public SubjectTypeObservationType getSubjectTypeObservation() {
        return subjectTypeObservation;
    }

    /**
     * 设置subjectTypeObservation属性的值。
     *
     * @param value allowed object is
     *              {@link SubjectTypeObservationType }
     */
    public void setSubjectTypeObservation(SubjectTypeObservationType value) {
        this.subjectTypeObservation = value;
    }

}
