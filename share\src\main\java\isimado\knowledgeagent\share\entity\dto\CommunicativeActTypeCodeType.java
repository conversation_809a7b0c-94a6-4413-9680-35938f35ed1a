//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>CommunicativeActTypeCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="CommunicativeActTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="Accept"/>
 *     &lt;enumeration value="Agree"/>
 *     &lt;enumeration value="Confirm"/>
 *     &lt;enumeration value="Inform"/>
 *     &lt;enumeration value="Propose"/>
 *     &lt;enumeration value="Refuse"/>
 *     &lt;enumeration value="Request"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum CommunicativeActTypeCodeType {

    ACCEPT("Accept"),
    AGREE("Agree"),
    CONFIRM("Confirm"),
    INFORM("Inform"),
    PROPOSE("Propose"),
    REFUSE("Refuse"),
    REQUEST("Request");
    private final String value;

    CommunicativeActTypeCodeType(String v) {
        value = v;
    }

    public static CommunicativeActTypeCodeType fromValue(String v) {
        for (CommunicativeActTypeCodeType c : CommunicativeActTypeCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
