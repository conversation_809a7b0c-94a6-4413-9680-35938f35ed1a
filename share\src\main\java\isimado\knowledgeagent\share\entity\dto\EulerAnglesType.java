//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#EulerAngles
 *
 * <p>EulerAnglesType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="EulerAnglesType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OrientationGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Phi"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Psi"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Theta"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EulerAnglesType {

    @JsonProperty(value = "HeadingAngle")
    protected double headingAngle;
    @JsonProperty(value = "Phi")
    protected double phi;
    @JsonProperty(value = "Psi")
    protected double psi;
    @JsonProperty(value = "Theta")
    protected double theta;

    /**
     * 获取headingAngle属性的值。
     */
    public double getHeadingAngle() {
        return headingAngle;
    }

    /**
     * 设置headingAngle属性的值。
     */
    public void setHeadingAngle(double value) {
        this.headingAngle = value;
    }

    /**
     * 获取phi属性的值。
     */
    public double getPhi() {
        return phi;
    }

    /**
     * 设置phi属性的值。
     */
    public void setPhi(double value) {
        this.phi = value;
    }

    /**
     * 获取psi属性的值。
     */
    public double getPsi() {
        return psi;
    }

    /**
     * 设置psi属性的值。
     */
    public void setPsi(double value) {
        this.psi = value;
    }

    /**
     * 获取theta属性的值。
     */
    public double getTheta() {
        return theta;
    }

    /**
     * 设置theta属性的值。
     */
    public void setTheta(double value) {
        this.theta = value;
    }

}
