//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>OrganizationTypeCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="OrganizationTypeCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="CIVCON"/>
 *     &lt;enumeration value="CNTRCT"/>
 *     &lt;enumeration value="CRIMIN"/>
 *     &lt;enumeration value="DSPLPR"/>
 *     &lt;enumeration value="EDUCAL"/>
 *     &lt;enumeration value="FINCAL"/>
 *     &lt;enumeration value="FRNFGT"/>
 *     &lt;enumeration value="GANG"/>
 *     &lt;enumeration value="INSRGT"/>
 *     &lt;enumeration value="INTEL"/>
 *     &lt;enumeration value="INTLCT"/>
 *     &lt;enumeration value="JRNLST"/>
 *     &lt;enumeration value="JUDCAL"/>
 *     &lt;enumeration value="LNDOWN"/>
 *     &lt;enumeration value="LOCINH"/>
 *     &lt;enumeration value="MEDCAL"/>
 *     &lt;enumeration value="MEDINT"/>
 *     &lt;enumeration value="MEDLCL"/>
 *     &lt;enumeration value="MEDNAT"/>
 *     &lt;enumeration value="MEDNOS"/>
 *     &lt;enumeration value="MRCHNT"/>
 *     &lt;enumeration value="NKN"/>
 *     &lt;enumeration value="NOS"/>
 *     &lt;enumeration value="POLCHF"/>
 *     &lt;enumeration value="POLICE"/>
 *     &lt;enumeration value="POLTCL"/>
 *     &lt;enumeration value="POW"/>
 *     &lt;enumeration value="PRSNR"/>
 *     &lt;enumeration value="REFUGE"/>
 *     &lt;enumeration value="SHURA"/>
 *     &lt;enumeration value="SOCIAL"/>
 *     &lt;enumeration value="TERRST"/>
 *     &lt;enumeration value="TRIBAL"/>
 *     &lt;enumeration value="VILELD"/>
 *     &lt;enumeration value="WRITER"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum OrganizationTypeCodeType {

    CIVCON,
    CNTRCT,
    CRIMIN,
    DSPLPR,
    EDUCAL,
    FINCAL,
    FRNFGT,
    GANG,
    INSRGT,
    INTEL,
    INTLCT,
    JRNLST,
    JUDCAL,
    LNDOWN,
    LOCINH,
    MEDCAL,
    MEDINT,
    MEDLCL,
    MEDNAT,
    MEDNOS,
    MRCHNT,
    NKN,
    NOS,
    POLCHF,
    POLICE,
    POLTCL,
    POW,
    PRSNR,
    REFUGE,
    SHURA,
    SOCIAL,
    TERRST,
    TRIBAL,
    VILELD,
    WRITER;

    public static OrganizationTypeCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
