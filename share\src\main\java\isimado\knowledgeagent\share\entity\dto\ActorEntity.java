package isimado.knowledgeagent.share.entity.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/1 9:43
 */
public interface ActorEntity extends Entity {

    /**
     * 当前任务
     *
     * @return
     */
    List<String> getCurrentTask();

    /**
     * 实体描述
     *
     * @return
     */
    EntityDescriptorType getEntityDescriptor();

    /**
     * 资源
     *
     * @return
     */
    List<ResourceType> getResource();

    String getOriginID();

    void setOriginID(String value);

    List<BasicDataItem> getBasicDataItem();

    void setBasicDataItem(List<BasicDataItem> basicDataItem);
}
