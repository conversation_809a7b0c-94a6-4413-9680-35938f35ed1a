spring:
  ai:
    openai:
      api-key: sk-5bae0ef310c84cfcaa81d1e51473b114
      base-url: https://api.deepseek.com
      chat:
        options:
          model: deepseek-chat
    vectorstore:
      pgvector:
        dimensions: 384
        table-name: c2sim_entity
    embedding:
      transformer:
        tokenizer:
          uri: file:///D:/project/ai/embedding/paraphrase-multilingual-MiniLM-L12-v2/tokenizer.json
        onnx:
          model-uri: file:///D:/project/ai/embedding/paraphrase-multilingual-MiniLM-L12-v2/model.onnx
  neo4j:
    uri: bolt://localhost:7687
  main:
    allow-circular-references: true
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *****************************************
    username: pgvector
    password: pgvector
