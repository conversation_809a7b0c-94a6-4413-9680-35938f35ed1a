package isimado.knowledgeagent.tool.context.mybatis;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

public class JsonTypeHandler extends AbstractJsonTypeHandler {
    public JsonTypeHandler(Class type) {
        super(type);
    }

    @Override
    public Object parse(String json) {
        return null;
    }

    @Override
    public String toJson(Object obj) {
        return "";
    }
}
