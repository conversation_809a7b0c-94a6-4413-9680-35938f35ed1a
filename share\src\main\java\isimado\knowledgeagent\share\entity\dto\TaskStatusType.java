//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#TaskStatus
 *
 * <p>TaskStatusType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="TaskStatusType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReportContentGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CurrentTask"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskStatusCode"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class TaskStatusType {

    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "TimeOfObservation", required = true)
    protected TimeInstantType timeOfObservation;
    @JsonProperty(value = "CurrentTask", required = true)
    protected String currentTask;
    @JsonProperty(value = "TaskStatusCode", required = true)

    protected TaskStatusCodeType taskStatusCode;

    /**
     * 获取duration属性的值。
     *
     * @return possible object is
     * {@link DurationType }
     */
    public DurationType getDuration() {
        return duration;
    }

    /**
     * 设置duration属性的值。
     *
     * @param value allowed object is
     *              {@link DurationType }
     */
    public void setDuration(DurationType value) {
        this.duration = value;
    }

    /**
     * 获取timeOfObservation属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getTimeOfObservation() {
        return timeOfObservation;
    }

    /**
     * 设置timeOfObservation属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setTimeOfObservation(TimeInstantType value) {
        this.timeOfObservation = value;
    }

    /**
     * 获取currentTask属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCurrentTask() {
        return currentTask;
    }

    /**
     * 设置currentTask属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCurrentTask(String value) {
        this.currentTask = value;
    }

    /**
     * 获取taskStatusCode属性的值。
     *
     * @return possible object is
     * {@link TaskStatusCodeType }
     */
    public TaskStatusCodeType getTaskStatusCode() {
        return taskStatusCode;
    }

    /**
     * 设置taskStatusCode属性的值。
     *
     * @param value allowed object is
     *              {@link TaskStatusCodeType }
     */
    public void setTaskStatusCode(TaskStatusCodeType value) {
        this.taskStatusCode = value;
    }

}
