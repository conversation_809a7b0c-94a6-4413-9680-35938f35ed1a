//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>HostilityStatusCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="HostilityStatusCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="AFR"/>
 *     &lt;enumeration value="AHO"/>
 *     &lt;enumeration value="AIV"/>
 *     &lt;enumeration value="ANT"/>
 *     &lt;enumeration value="FAKER"/>
 *     &lt;enumeration value="FR"/>
 *     &lt;enumeration value="HO"/>
 *     &lt;enumeration value="IV"/>
 *     &lt;enumeration value="JOKER"/>
 *     &lt;enumeration value="NEUTRL"/>
 *     &lt;enumeration value="PENDNG"/>
 *     &lt;enumeration value="SUSPCT"/>
 *     &lt;enumeration value="UNK"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum HostilityStatusCodeType {

    AFR,
    AHO,
    AIV,
    ANT,
    FAKER,
    FR,
    HO,
    IV,
    JOKER,
    NEUTRL,
    PENDNG,
    SUSPCT,
    UNK;

    public static HostilityStatusCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
