package isimado.knowledgeagent.tool.context;


import isimado.knowledgeagent.tool.service.C2simEntityService;
import isimado.knowledgeagent.tool.service.DocumentRetrievalService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MCP服务器配置类，负责注册MCP工具
 */
@Configuration
public class McpServerConfig {

    /**
     * @param c2simEntityService
     * @return 工具回调提供者
     */
    @Bean
    public ToolCallbackProvider bookToolCallbackProvider(C2simEntityService c2simEntityService,
                                                         DocumentRetrievalService documentRetrievalService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(c2simEntityService, documentRetrievalService)
                .build();
    }

}
