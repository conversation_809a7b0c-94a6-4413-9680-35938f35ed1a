//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#OrganizationCode
 *
 * <p>OrganizationCodeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="OrganizationCodeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CountryCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EthnicGroupCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OrganizationTypeCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReligionCode"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class OrganizationCodeType {

    @JsonProperty(value = "CountryCode")

    protected CountryCodeType countryCode;
    @JsonProperty(value = "EthnicGroupCode")
    protected String ethnicGroupCode;
    @JsonProperty(value = "OrganizationTypeCode")

    protected OrganizationTypeCodeType organizationTypeCode;
    @JsonProperty(value = "ReligionCode")

    protected ReligionCodeType religionCode;

    /**
     * 获取countryCode属性的值。
     *
     * @return possible object is
     * {@link CountryCodeType }
     */
    public CountryCodeType getCountryCode() {
        return countryCode;
    }

    /**
     * 设置countryCode属性的值。
     *
     * @param value allowed object is
     *              {@link CountryCodeType }
     */
    public void setCountryCode(CountryCodeType value) {
        this.countryCode = value;
    }

    /**
     * 获取ethnicGroupCode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getEthnicGroupCode() {
        return ethnicGroupCode;
    }

    /**
     * 设置ethnicGroupCode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setEthnicGroupCode(String value) {
        this.ethnicGroupCode = value;
    }

    /**
     * 获取organizationTypeCode属性的值。
     *
     * @return possible object is
     * {@link OrganizationTypeCodeType }
     */
    public OrganizationTypeCodeType getOrganizationTypeCode() {
        return organizationTypeCode;
    }

    /**
     * 设置organizationTypeCode属性的值。
     *
     * @param value allowed object is
     *              {@link OrganizationTypeCodeType }
     */
    public void setOrganizationTypeCode(OrganizationTypeCodeType value) {
        this.organizationTypeCode = value;
    }

    /**
     * 获取religionCode属性的值。
     *
     * @return possible object is
     * {@link ReligionCodeType }
     */
    public ReligionCodeType getReligionCode() {
        return religionCode;
    }

    /**
     * 设置religionCode属性的值。
     *
     * @param value allowed object is
     *              {@link ReligionCodeType }
     */
    public void setReligionCode(ReligionCodeType value) {
        this.religionCode = value;
    }

}
