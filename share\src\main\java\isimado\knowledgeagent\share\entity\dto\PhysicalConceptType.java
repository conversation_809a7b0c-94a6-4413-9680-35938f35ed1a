//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#PhysicalConcept
 *
 * <p>PhysicalConceptType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PhysicalConceptType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityHealthStatus"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Location"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Orientation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SpatialOffset"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TemporalConcept"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PhysicalConceptType {

    @JsonProperty(value = "EntityHealthStatus")
    protected EntityHealthStatusType entityHealthStatus;
    @JsonProperty(value = "Location")
    protected LocationType location;
    @JsonProperty(value = "Orientation")
    protected OrientationType orientation;
    @JsonProperty(value = "SpatialOffset")
    protected SpatialOffsetType spatialOffset;
    @JsonProperty(value = "TemporalConcept")
    protected TemporalConceptType temporalConcept;

    /**
     * 获取entityHealthStatus属性的值。
     *
     * @return possible object is
     * {@link EntityHealthStatusType }
     */
    public EntityHealthStatusType getEntityHealthStatus() {
        return entityHealthStatus;
    }

    /**
     * 设置entityHealthStatus属性的值。
     *
     * @param value allowed object is
     *              {@link EntityHealthStatusType }
     */
    public void setEntityHealthStatus(EntityHealthStatusType value) {
        this.entityHealthStatus = value;
    }

    /**
     * 获取location属性的值。
     *
     * @return possible object is
     * {@link LocationType }
     */
    public LocationType getLocation() {
        return location;
    }

    /**
     * 设置location属性的值。
     *
     * @param value allowed object is
     *              {@link LocationType }
     */
    public void setLocation(LocationType value) {
        this.location = value;
    }

    /**
     * 获取orientation属性的值。
     *
     * @return possible object is
     * {@link OrientationType }
     */
    public OrientationType getOrientation() {
        return orientation;
    }

    /**
     * 设置orientation属性的值。
     *
     * @param value allowed object is
     *              {@link OrientationType }
     */
    public void setOrientation(OrientationType value) {
        this.orientation = value;
    }

    /**
     * 获取spatialOffset属性的值。
     *
     * @return possible object is
     * {@link SpatialOffsetType }
     */
    public SpatialOffsetType getSpatialOffset() {
        return spatialOffset;
    }

    /**
     * 设置spatialOffset属性的值。
     *
     * @param value allowed object is
     *              {@link SpatialOffsetType }
     */
    public void setSpatialOffset(SpatialOffsetType value) {
        this.spatialOffset = value;
    }

    /**
     * 获取temporalConcept属性的值。
     *
     * @return possible object is
     * {@link TemporalConceptType }
     */
    public TemporalConceptType getTemporalConcept() {
        return temporalConcept;
    }

    /**
     * 设置temporalConcept属性的值。
     *
     * @param value allowed object is
     *              {@link TemporalConceptType }
     */
    public void setTemporalConcept(TemporalConceptType value) {
        this.temporalConcept = value;
    }

}
