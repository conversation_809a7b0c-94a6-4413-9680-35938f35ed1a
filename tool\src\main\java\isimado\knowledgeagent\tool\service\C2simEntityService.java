package isimado.knowledgeagent.tool.service;


import isimado.knowledgeagent.share.entity.dto.Platform;
import isimado.knowledgeagent.tool.entity.dto.C2simXmlValidResultDTO;

import java.util.List;

public interface C2simEntityService {


    /**
     * 验证JSON
     *
     * @param jsonString
     */
    void validateC2simEntity(String jsonString);


    /**
     * 验证XML
     *
     * @param xmlString
     */
    C2simXmlValidResultDTO validateC2simXml(String xmlString);


    /**
     * 保存数据
     *
     * @param platform
     */
    void saveC2simEntity(Platform platform);


    /**
     * 搜索
     * @param limit 检索数量
     * @param text
     * @param minSimilarity 最小相似度
     * @return
     */
    List<Platform> searchC2simEntity(int limit, String text, double minSimilarity);
}
