//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>ActionTemporalAssociationCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="ActionTemporalAssociationCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ENDEND"/>
 *     &lt;enumeration value="ENDENE"/>
 *     &lt;enumeration value="ENDENL"/>
 *     &lt;enumeration value="ENDSNE"/>
 *     &lt;enumeration value="ENDSNL"/>
 *     &lt;enumeration value="ENDSTR"/>
 *     &lt;enumeration value="SAEAST"/>
 *     &lt;enumeration value="SAENDO"/>
 *     &lt;enumeration value="SASTEA"/>
 *     &lt;enumeration value="SBEAST"/>
 *     &lt;enumeration value="SDUREA"/>
 *     &lt;enumeration value="SDUREB"/>
 *     &lt;enumeration value="STREND"/>
 *     &lt;enumeration value="STRENE"/>
 *     &lt;enumeration value="STRENL"/>
 *     &lt;enumeration value="STRSNE"/>
 *     &lt;enumeration value="STRSNL"/>
 *     &lt;enumeration value="STRSTR"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum ActionTemporalAssociationCodeType {

    ENDEND,
    ENDENE,
    ENDENL,
    ENDSNE,
    ENDSNL,
    ENDSTR,
    SAEAST,
    SAENDO,
    SASTEA,
    SBEAST,
    SDUREA,
    SDUREB,
    STREND,
    STRENE,
    STRENL,
    STRSNE,
    STRSNL,
    STRSTR;

    public static ActionTemporalAssociationCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
