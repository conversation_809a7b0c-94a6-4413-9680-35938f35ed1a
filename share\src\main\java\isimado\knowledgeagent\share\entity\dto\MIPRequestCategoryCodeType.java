//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>MIPRequestCategoryCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="MIPRequestCategoryCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ACTION"/>
 *     &lt;enumeration value="ASSOC"/>
 *     &lt;enumeration value="CAPAB"/>
 *     &lt;enumeration value="HOLDNG"/>
 *     &lt;enumeration value="HOSTIL"/>
 *     &lt;enumeration value="LOCATN"/>
 *     &lt;enumeration value="PRESNC"/>
 *     &lt;enumeration value="STATUS"/>
 *     &lt;enumeration value="TYPE"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum MIPRequestCategoryCodeType {

    ACTION,
    ASSOC,
    CAPAB,
    HOLDNG,
    HOSTIL,
    LOCATN,
    PRESNC,
    STATUS,
    TYPE;

    public static MIPRequestCategoryCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
