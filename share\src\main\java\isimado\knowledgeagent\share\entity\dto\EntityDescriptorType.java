//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/C2SIM#EntityDescriptor
 *
 * <p>EntityDescriptorType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="EntityDescriptorType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AffiliatedWith" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AllegianceRelationship" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CommunicationsNetwork" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Side" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Superior" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EntityDescriptorType {

    @JsonProperty(value = "AffiliatedWith")
    protected List<String> affiliatedWith;
    @JsonProperty(value = "AllegianceRelationship")
    protected List<AllegianceRelationshipType> allegianceRelationship;
    @JsonProperty(value = "CommunicationsNetwork")
    protected List<String> communicationsNetwork;
    @JsonProperty(value = "Side")
    protected String side;
    @JsonProperty(value = "Superior")
    protected String superior;

    /**
     * Gets the value of the affiliatedWith property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the affiliatedWith property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAffiliatedWith().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getAffiliatedWith() {
        if (affiliatedWith == null) {
            affiliatedWith = new ArrayList<>();
        }
        return this.affiliatedWith;
    }

    /**
     * Gets the value of the allegianceRelationship property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the allegianceRelationship property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAllegianceRelationship().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AllegianceRelationshipType }
     */
    public List<AllegianceRelationshipType> getAllegianceRelationship() {
        if (allegianceRelationship == null) {
            allegianceRelationship = new ArrayList<>();
        }
        return this.allegianceRelationship;
    }

    /**
     * Gets the value of the communicationsNetwork property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the communicationsNetwork property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCommunicationsNetwork().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getCommunicationsNetwork() {
        if (communicationsNetwork == null) {
            communicationsNetwork = new ArrayList<>();
        }
        return this.communicationsNetwork;
    }
}
