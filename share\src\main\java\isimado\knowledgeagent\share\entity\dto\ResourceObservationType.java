//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/smx#ResourceObservation
 *
 * <p>ResourceObservationType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ResourceObservationType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ObservationGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Resource" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class ResourceObservationType {

    @JsonProperty(value = "ActorReference")
    protected String actorReference;
    @JsonProperty(value = "ConfidenceLevel")
    protected Double confidenceLevel;
    @JsonProperty(value = "UncertaintyInterval")
    protected Double uncertaintyInterval;
    @JsonProperty(value = "Resource", required = true)
    protected List<ResourceType> resource;

    /**
     * 获取actorReference属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getActorReference() {
        return actorReference;
    }

    /**
     * 设置actorReference属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setActorReference(String value) {
        this.actorReference = value;
    }

    /**
     * 获取confidenceLevel属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getConfidenceLevel() {
        return confidenceLevel;
    }

    /**
     * 设置confidenceLevel属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setConfidenceLevel(Double value) {
        this.confidenceLevel = value;
    }

    /**
     * 获取uncertaintyInterval属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getUncertaintyInterval() {
        return uncertaintyInterval;
    }

    /**
     * 设置uncertaintyInterval属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setUncertaintyInterval(Double value) {
        this.uncertaintyInterval = value;
    }

    /**
     * Gets the value of the resource property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the resource property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getResource().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ResourceType }
     */
    public List<ResourceType> getResource() {
        if (resource == null) {
            resource = new ArrayList<ResourceType>();
        }
        return this.resource;
    }

}
