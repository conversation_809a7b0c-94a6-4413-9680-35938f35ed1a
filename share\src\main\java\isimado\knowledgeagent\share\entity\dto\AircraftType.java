//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * http://www.sisostds.org/ontologies/smx#Aircraft
 *
 * <p>AircraftType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="AircraftType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActorEntityGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlatformGroup"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class AircraftType extends Platform {

    @JsonProperty
    protected List<String> currentTask;
    @JsonProperty(value = "EntityDescriptor", required = true)
    protected EntityDescriptorType entityDescriptor;
    @JsonProperty(value = "Resource")
    protected List<ResourceType> resource;

    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "CurrentState")
    protected EntityStateType currentState;
    @JsonProperty(value = "Marking")
    protected String marking;
    @JsonProperty(value = "OriginID")
    protected String originId;
    @JsonProperty(value = "BasicDataItem")
    protected List<BasicDataItem> basicDataItem;

    /**
     * Gets the value of the currentTask property.
     */
    @Override
    public List<String> getCurrentTask() {
        if (currentTask == null) {
            currentTask = new ArrayList<String>();
        }
        return this.currentTask;
    }

    /**
     * Gets the value of the resource property.
     */
    @Override
    public List<ResourceType> getResource() {
        if (resource == null) {
            resource = new ArrayList<ResourceType>();
        }
        return this.resource;
    }



    @Override
    public String getOriginID() {
        return this.originId;
    }

    @Override
    public void setOriginID(String value) {
        this.originId = value;
    }

    @Override
    public List<BasicDataItem> getBasicDataItem() {
        return this.basicDataItem;
    }

    @Override
    public void setBasicDataItem(List<BasicDataItem> basicDataItem) {
        this.basicDataItem = basicDataItem;
    }
}
