package isimado.knowledgeagent.tool.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import isimado.framework.base.Resp;
import isimado.framework.util.JsonUtils;
import isimado.knowledgeagent.tool.entity.dto.C2simXmlValidResultDTO;
import isimado.knowledgeagent.tool.service.C2simEntityService;
import isimado.knowledgeagent.share.entity.dto.Platform;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("c2sim")
public class C2simEntityController {

    @Resource
    private C2simEntityService c2simEntityService;


    @PostMapping("saveActorEntity")
    public Resp<Platform> saveActorEntity(@RequestBody Map entityMap) {

        Platform platform = null;
        String json = JsonUtils.toJson(entityMap);
        if (entityMap.size() == 1) {
            Map<String, Platform> r = JsonUtils.parse(json, new TypeReference<>() {
            });
            platform = r.values().iterator().next();
        } else {
            platform = JsonUtils.parse(json, Platform.class);
        }
        c2simEntityService.saveC2simEntity(platform);

        return Resp.success(platform);
    }

    @GetMapping("searchActorEntity")
    public Resp<List<Platform>> searchActorEntity(@RequestParam(required = false) Integer limit, @RequestParam String text, @RequestParam(required = false) Double minSimilarity) {
        if (limit == null) {
            limit = 5;
        }
        if (minSimilarity == null) {
            minSimilarity = 0.0;
        }
        List<Platform> results = c2simEntityService.searchC2simEntity(limit, text, minSimilarity);
        return Resp.success(results);
    }


    @PostMapping("validateC2simXml")
    public Resp<C2simXmlValidResultDTO> validateC2simXml(@RequestBody String xml) {
        return Resp.success(c2simEntityService.validateC2simXml(xml));
    }

}
