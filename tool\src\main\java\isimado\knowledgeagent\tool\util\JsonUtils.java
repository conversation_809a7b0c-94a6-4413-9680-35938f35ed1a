package isimado.knowledgeagent.tool.util;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static org.springframework.util.ObjectUtils.isEmpty;

/**
 * <AUTHOR>
 */
public class JsonUtils {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        MAPPER.setVisibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE);
        MAPPER.setVisibility(PropertyAccessor.IS_GETTER, JsonAutoDetect.Visibility.NONE);
        MAPPER.setVisibility(PropertyAccessor.SETTER, JsonAutoDetect.Visibility.NONE);

        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        MAPPER.registerModule(javaTimeModule);
    }

    public static ObjectMapper getObjectMapper() {
        return MAPPER;
    }

    @SneakyThrows
    public static String toJson(Object data) {
        if (data == null) {
            return null;
        }
        return MAPPER.writeValueAsString(data);
    }

    @SneakyThrows
    public static <T> T fromJson(String jsonData, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonData) || clazz == null) {
            return null;
        }
        return MAPPER.readValue(jsonData, clazz);
    }

    /**
     * 反序列化
     *
     * @param jsonData
     * @param clazz
     * @param <T>
     * @return
     */
    @SneakyThrows
    public static <T> T fromJson(byte[] jsonData, Class<T> clazz) {
        if (isEmpty(jsonData) || clazz == null) {
            return null;
        }
        return MAPPER.readValue(jsonData, clazz);
    }

    @SneakyThrows
    public static Map<?, ?> toMap(String string) {
        if (StringUtils.isEmpty(string)) {
            return null;
        }
        return MAPPER.readValue(string, Map.class);
    }

    @SneakyThrows
    public static List<?> toList(String string) {
        if (StringUtils.isEmpty(string)) {
            return null;
        }
        return MAPPER.readValue(string, List.class);
    }

    @SneakyThrows
    public static <T> T parse(String result, TypeReference<T> reference) {
        if (StringUtils.isEmpty(result) || reference == null) {
            return null;
        }
        return MAPPER.readValue(result, reference);
    }

    @SneakyThrows
    public static <T> T parse(String result, Class<T> clazz) {
        if (StringUtils.isEmpty(result) || clazz == null) {
            return null;
        }
        return MAPPER.readValue(result, clazz);
    }


    @SneakyThrows
    public static <T> T parse(byte[] result, TypeReference<T> reference) {
        if (result == null || reference == null) {
            return null;
        }
        return MAPPER.readValue(result, reference);
    }
}
