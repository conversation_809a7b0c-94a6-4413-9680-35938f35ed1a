//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>ReligionCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="ReligionCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="AFRMTH"/>
 *     &lt;enumeration value="ANGLCN"/>
 *     &lt;enumeration value="ANIMSM"/>
 *     &lt;enumeration value="APSTLC"/>
 *     &lt;enumeration value="ARMNAN"/>
 *     &lt;enumeration value="ARMORT"/>
 *     &lt;enumeration value="ASMGOD"/>
 *     &lt;enumeration value="ATHEST"/>
 *     &lt;enumeration value="BABYLN"/>
 *     &lt;enumeration value="BAHAI"/>
 *     &lt;enumeration value="BAPTST"/>
 *     &lt;enumeration value="BEHAI"/>
 *     &lt;enumeration value="BLGORT"/>
 *     &lt;enumeration value="BUDHSM"/>
 *     &lt;enumeration value="CALVNS"/>
 *     &lt;enumeration value="CATHLC"/>
 *     &lt;enumeration value="CHNDGY"/>
 *     &lt;enumeration value="CHRENG"/>
 *     &lt;enumeration value="CHRFRW"/>
 *     &lt;enumeration value="CHRGOD"/>
 *     &lt;enumeration value="CHRSLB"/>
 *     &lt;enumeration value="CHRSTN"/>
 *     &lt;enumeration value="CHRTVL"/>
 *     &lt;enumeration value="CNFCNS"/>
 *     &lt;enumeration value="CNG"/>
 *     &lt;enumeration value="CNGCCH"/>
 *     &lt;enumeration value="CNGNCH"/>
 *     &lt;enumeration value="CPTCHR"/>
 *     &lt;enumeration value="DRUZE"/>
 *     &lt;enumeration value="EKLNIE"/>
 *     &lt;enumeration value="EPSCPL"/>
 *     &lt;enumeration value="ESTORT"/>
 *     &lt;enumeration value="ETHORT"/>
 *     &lt;enumeration value="EVNALL"/>
 *     &lt;enumeration value="EVNGCH"/>
 *     &lt;enumeration value="EVNLUT"/>
 *     &lt;enumeration value="GEOORT"/>
 *     &lt;enumeration value="GRGARM"/>
 *     &lt;enumeration value="GRKCTH"/>
 *     &lt;enumeration value="GRKORT"/>
 *     &lt;enumeration value="HINDU"/>
 *     &lt;enumeration value="IBDMUS"/>
 *     &lt;enumeration value="INDGNS"/>
 *     &lt;enumeration value="ISLAMA"/>
 *     &lt;enumeration value="ISLAMC"/>
 *     &lt;enumeration value="ISLAMI"/>
 *     &lt;enumeration value="ISLAMN"/>
 *     &lt;enumeration value="JAINS"/>
 *     &lt;enumeration value="JHVWTN"/>
 *     &lt;enumeration value="JUDASM"/>
 *     &lt;enumeration value="KIEVPT"/>
 *     &lt;enumeration value="KMBNGS"/>
 *     &lt;enumeration value="LAMBUD"/>
 *     &lt;enumeration value="LNDMSN"/>
 *     &lt;enumeration value="LTRSNT"/>
 *     &lt;enumeration value="LUTHRN"/>
 *     &lt;enumeration value="MANDEA"/>
 *     &lt;enumeration value="MARONT"/>
 *     &lt;enumeration value="MENNTE"/>
 *     &lt;enumeration value="METHDS"/>
 *     &lt;enumeration value="MODKNG"/>
 *     &lt;enumeration value="MORMON"/>
 *     &lt;enumeration value="MSCWPT"/>
 *     &lt;enumeration value="MUSLIM"/>
 *     &lt;enumeration value="MUSMLY"/>
 *     &lt;enumeration value="MUSSHF"/>
 *     &lt;enumeration value="MUSZYD"/>
 *     &lt;enumeration value="NIUNCH"/>
 *     &lt;enumeration value="NKN"/>
 *     &lt;enumeration value="NONE"/>
 *     &lt;enumeration value="NOS"/>
 *     &lt;enumeration value="ORTHDX"/>
 *     &lt;enumeration value="PARSI"/>
 *     &lt;enumeration value="PGNAFR"/>
 *     &lt;enumeration value="PNTCST"/>
 *     &lt;enumeration value="PRSBYT"/>
 *     &lt;enumeration value="PRT"/>
 *     &lt;enumeration value="PRTADV"/>
 *     &lt;enumeration value="PRTEVN"/>
 *     &lt;enumeration value="PRTMRV"/>
 *     &lt;enumeration value="RMNCTH"/>
 *     &lt;enumeration value="RMNCTU"/>
 *     &lt;enumeration value="ROMORT"/>
 *     &lt;enumeration value="RUSORT"/>
 *     &lt;enumeration value="SHIMUS"/>
 *     &lt;enumeration value="SHINTO"/>
 *     &lt;enumeration value="SHMNSM"/>
 *     &lt;enumeration value="SIKH"/>
 *     &lt;enumeration value="SLVMUS"/>
 *     &lt;enumeration value="SOCFRD"/>
 *     &lt;enumeration value="SPRCLT"/>
 *     &lt;enumeration value="SUNMUS"/>
 *     &lt;enumeration value="SVNADV"/>
 *     &lt;enumeration value="SYNCHN"/>
 *     &lt;enumeration value="TAOISM"/>
 *     &lt;enumeration value="TBTBUD"/>
 *     &lt;enumeration value="THRVBD"/>
 *     &lt;enumeration value="TRBREL"/>
 *     &lt;enumeration value="TRD"/>
 *     &lt;enumeration value="TRDMYN"/>
 *     &lt;enumeration value="UKRAUT"/>
 *     &lt;enumeration value="UKRCTH"/>
 *     &lt;enumeration value="UKRORT"/>
 *     &lt;enumeration value="UNCH"/>
 *     &lt;enumeration value="UNCHAU"/>
 *     &lt;enumeration value="UNCHCN"/>
 *     &lt;enumeration value="UNCHPR"/>
 *     &lt;enumeration value="UNDMTH"/>
 *     &lt;enumeration value="UNDPRB"/>
 *     &lt;enumeration value="UNFRCH"/>
 *     &lt;enumeration value="UNICTH"/>
 *     &lt;enumeration value="VOODOO"/>
 *     &lt;enumeration value="ZRSTRN"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum ReligionCodeType {

    AFRMTH,
    ANGLCN,
    ANIMSM,
    APSTLC,
    ARMNAN,
    ARMORT,
    ASMGOD,
    ATHEST,
    BABYLN,
    BAHAI,
    BAPTST,
    BEHAI,
    BLGORT,
    BUDHSM,
    CALVNS,
    CATHLC,
    CHNDGY,
    CHRENG,
    CHRFRW,
    CHRGOD,
    CHRSLB,
    CHRSTN,
    CHRTVL,
    CNFCNS,
    CNG,
    CNGCCH,
    CNGNCH,
    CPTCHR,
    DRUZE,
    EKLNIE,
    EPSCPL,
    ESTORT,
    ETHORT,
    EVNALL,
    EVNGCH,
    EVNLUT,
    GEOORT,
    GRGARM,
    GRKCTH,
    GRKORT,
    HINDU,
    IBDMUS,
    INDGNS,
    ISLAMA,
    ISLAMC,
    ISLAMI,
    ISLAMN,
    JAINS,
    JHVWTN,
    JUDASM,
    KIEVPT,
    KMBNGS,
    LAMBUD,
    LNDMSN,
    LTRSNT,
    LUTHRN,
    MANDEA,
    MARONT,
    MENNTE,
    METHDS,
    MODKNG,
    MORMON,
    MSCWPT,
    MUSLIM,
    MUSMLY,
    MUSSHF,
    MUSZYD,
    NIUNCH,
    NKN,
    NONE,
    NOS,
    ORTHDX,
    PARSI,
    PGNAFR,
    PNTCST,
    PRSBYT,
    PRT,
    PRTADV,
    PRTEVN,
    PRTMRV,
    RMNCTH,
    RMNCTU,
    ROMORT,
    RUSORT,
    SHIMUS,
    SHINTO,
    SHMNSM,
    SIKH,
    SLVMUS,
    SOCFRD,
    SPRCLT,
    SUNMUS,
    SVNADV,
    SYNCHN,
    TAOISM,
    TBTBUD,
    THRVBD,
    TRBREL,
    TRD,
    TRDMYN,
    UKRAUT,
    UKRCTH,
    UKRORT,
    UNCH,
    UNCHAU,
    UNCHCN,
    UNCHPR,
    UNDMTH,
    UNDPRB,
    UNFRCH,
    UNICTH,
    VOODOO,
    ZRSTRN;

    public static ReligionCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
