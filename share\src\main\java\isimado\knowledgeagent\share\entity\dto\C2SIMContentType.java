//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#C2SIMContent
 *
 * <p>C2SIMContentType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="C2SIMContentType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AbstractObject"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Action"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Code"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Entity"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityDescriptor"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityState"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityType"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Observation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PhysicalConcept"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhase"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhaseTrigger"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Relationship"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Resource"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}RuleOfEngagement"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class C2SIMContentType {

    @JsonProperty(value = "AbstractObject")
    protected AbstractObjectType abstractObject;
    @JsonProperty(value = "Action")
    protected ActionType action;
    @JsonProperty(value = "Code")
    protected CodeType code;
    @JsonProperty(value = "Entity")
    protected EntityType entity;
    @JsonProperty(value = "EntityDescriptor")
    protected EntityDescriptorType entityDescriptor;
    @JsonProperty(value = "EntityState")
    protected EntityStateType entityState;
    @JsonProperty(value = "EntityType")
    protected EntityTypeType entityType;
    @JsonProperty(value = "Observation")
    protected ObservationType observation;
    @JsonProperty(value = "PhysicalConcept")
    protected PhysicalConceptType physicalConcept;
    @JsonProperty(value = "PlanPhase")
    protected PlanPhaseType planPhase;
    @JsonProperty(value = "PlanPhaseTrigger")
    protected PlanPhaseTriggerType planPhaseTrigger;
    @JsonProperty(value = "Relationship")
    protected RelationshipType relationship;
    @JsonProperty(value = "Resource")
    protected ResourceType resource;
    @JsonProperty(value = "RuleOfEngagement")
    protected RuleOfEngagementType ruleOfEngagement;

    /**
     * 获取abstractObject属性的值。
     *
     * @return possible object is
     * {@link AbstractObjectType }
     */
    public AbstractObjectType getAbstractObject() {
        return abstractObject;
    }

    /**
     * 设置abstractObject属性的值。
     *
     * @param value allowed object is
     *              {@link AbstractObjectType }
     */
    public void setAbstractObject(AbstractObjectType value) {
        this.abstractObject = value;
    }

    /**
     * 获取action属性的值。
     *
     * @return possible object is
     * {@link ActionType }
     */
    public ActionType getAction() {
        return action;
    }

    /**
     * 设置action属性的值。
     *
     * @param value allowed object is
     *              {@link ActionType }
     */
    public void setAction(ActionType value) {
        this.action = value;
    }

    /**
     * 获取code属性的值。
     *
     * @return possible object is
     * {@link CodeType }
     */
    public CodeType getCode() {
        return code;
    }

    /**
     * 设置code属性的值。
     *
     * @param value allowed object is
     *              {@link CodeType }
     */
    public void setCode(CodeType value) {
        this.code = value;
    }

    /**
     * 获取entity属性的值。
     *
     * @return possible object is
     * {@link EntityType }
     */
    public EntityType getEntity() {
        return entity;
    }

    /**
     * 设置entity属性的值。
     *
     * @param value allowed object is
     *              {@link EntityType }
     */
    public void setEntity(EntityType value) {
        this.entity = value;
    }

    /**
     * 获取entityDescriptor属性的值。
     *
     * @return possible object is
     * {@link EntityDescriptorType }
     */
    public EntityDescriptorType getEntityDescriptor() {
        return entityDescriptor;
    }

    /**
     * 设置entityDescriptor属性的值。
     *
     * @param value allowed object is
     *              {@link EntityDescriptorType }
     */
    public void setEntityDescriptor(EntityDescriptorType value) {
        this.entityDescriptor = value;
    }

    /**
     * 获取entityState属性的值。
     *
     * @return possible object is
     * {@link EntityStateType }
     */
    public EntityStateType getEntityState() {
        return entityState;
    }

    /**
     * 设置entityState属性的值。
     *
     * @param value allowed object is
     *              {@link EntityStateType }
     */
    public void setEntityState(EntityStateType value) {
        this.entityState = value;
    }

    /**
     * 获取entityType属性的值。
     *
     * @return possible object is
     * {@link EntityTypeType }
     */
    public EntityTypeType getEntityType() {
        return entityType;
    }

    /**
     * 设置entityType属性的值。
     *
     * @param value allowed object is
     *              {@link EntityTypeType }
     */
    public void setEntityType(EntityTypeType value) {
        this.entityType = value;
    }

    /**
     * 获取observation属性的值。
     *
     * @return possible object is
     * {@link ObservationType }
     */
    public ObservationType getObservation() {
        return observation;
    }

    /**
     * 设置observation属性的值。
     *
     * @param value allowed object is
     *              {@link ObservationType }
     */
    public void setObservation(ObservationType value) {
        this.observation = value;
    }

    /**
     * 获取physicalConcept属性的值。
     *
     * @return possible object is
     * {@link PhysicalConceptType }
     */
    public PhysicalConceptType getPhysicalConcept() {
        return physicalConcept;
    }

    /**
     * 设置physicalConcept属性的值。
     *
     * @param value allowed object is
     *              {@link PhysicalConceptType }
     */
    public void setPhysicalConcept(PhysicalConceptType value) {
        this.physicalConcept = value;
    }

    /**
     * 获取planPhase属性的值。
     *
     * @return possible object is
     * {@link PlanPhaseType }
     */
    public PlanPhaseType getPlanPhase() {
        return planPhase;
    }

    /**
     * 设置planPhase属性的值。
     *
     * @param value allowed object is
     *              {@link PlanPhaseType }
     */
    public void setPlanPhase(PlanPhaseType value) {
        this.planPhase = value;
    }

    /**
     * 获取planPhaseTrigger属性的值。
     *
     * @return possible object is
     * {@link PlanPhaseTriggerType }
     */
    public PlanPhaseTriggerType getPlanPhaseTrigger() {
        return planPhaseTrigger;
    }

    /**
     * 设置planPhaseTrigger属性的值。
     *
     * @param value allowed object is
     *              {@link PlanPhaseTriggerType }
     */
    public void setPlanPhaseTrigger(PlanPhaseTriggerType value) {
        this.planPhaseTrigger = value;
    }

    /**
     * 获取relationship属性的值。
     *
     * @return possible object is
     * {@link RelationshipType }
     */
    public RelationshipType getRelationship() {
        return relationship;
    }

    /**
     * 设置relationship属性的值。
     *
     * @param value allowed object is
     *              {@link RelationshipType }
     */
    public void setRelationship(RelationshipType value) {
        this.relationship = value;
    }

    /**
     * 获取resource属性的值。
     *
     * @return possible object is
     * {@link ResourceType }
     */
    public ResourceType getResource() {
        return resource;
    }

    /**
     * 设置resource属性的值。
     *
     * @param value allowed object is
     *              {@link ResourceType }
     */
    public void setResource(ResourceType value) {
        this.resource = value;
    }

    /**
     * 获取ruleOfEngagement属性的值。
     *
     * @return possible object is
     * {@link RuleOfEngagementType }
     */
    public RuleOfEngagementType getRuleOfEngagement() {
        return ruleOfEngagement;
    }

    /**
     * 设置ruleOfEngagement属性的值。
     *
     * @param value allowed object is
     *              {@link RuleOfEngagementType }
     */
    public void setRuleOfEngagement(RuleOfEngagementType value) {
        this.ruleOfEngagement = value;
    }

}
