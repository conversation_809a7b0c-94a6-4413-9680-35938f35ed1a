//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/lox#ManeuverWarfareTask
 *
 * <p>ManeuverWarfareTaskType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ManeuverWarfareTaskType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}RuleOfEngagement" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskFunctionalRelation" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class ManeuverWarfareTaskType implements Action {

    @JsonProperty(value = "ActionTemporalRelationship")
    protected List<ActionTemporalRelationshipType> actionTemporalRelationship;
    @JsonProperty(value = "Location")
    protected List<LocationType> location;
    @JsonProperty(value = "MapGraphicID")
    protected List<String> mapGraphicID;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "AffectedEntity")
    protected List<String> affectedEntity;
    @JsonProperty(value = "DesiredEffectCode")

    protected List<DesiredEffectCodeType> desiredEffectCode;
    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "EndTime")
    protected TimeInstantType endTime;
    @JsonProperty(value = "PerformingEntity", required = true)
    protected String performingEntity;
    @JsonProperty(value = "StartTime")
    protected TimeInstantType startTime;
    @JsonProperty(value = "TaskActionCode", required = true)
    protected String taskActionCode;
    @JsonProperty(value = "RuleOfEngagement")
    protected List<RuleOfEngagementType> ruleOfEngagement;
    @JsonProperty(value = "TaskFunctionalRelation")
    protected List<TaskFunctionalRelationType> taskFunctionalRelation;
    @JsonProperty(value = "BasicDataItem")
    protected List<BasicDataItem> BasicDataItem;

    /**
     * Gets the value of the actionTemporalRelationship property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the actionTemporalRelationship property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getActionTemporalRelationship().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ActionTemporalRelationshipType }
     */
    public List<ActionTemporalRelationshipType> getActionTemporalRelationship() {
        if (actionTemporalRelationship == null) {
            actionTemporalRelationship = new ArrayList<ActionTemporalRelationshipType>();
        }
        return this.actionTemporalRelationship;
    }

    /**
     * Gets the value of the location property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the location property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLocation().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocationType }
     */
    public List<LocationType> getLocation() {
        if (location == null) {
            location = new ArrayList<LocationType>();
        }
        return this.location;
    }

    /**
     * Gets the value of the mapGraphicID property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the mapGraphicID property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMapGraphicID().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getMapGraphicID() {
        if (mapGraphicID == null) {
            mapGraphicID = new ArrayList<String>();
        }
        return this.mapGraphicID;
    }

    /**
     * 获取name属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * 获取uuid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getUUID() {
        return uuid;
    }

    /**
     * 设置uuid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUUID(String value) {
        this.uuid = value;
    }

    /**
     * Gets the value of the affectedEntity property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the affectedEntity property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAffectedEntity().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getAffectedEntity() {
        if (affectedEntity == null) {
            affectedEntity = new ArrayList<String>();
        }
        return this.affectedEntity;
    }

    /**
     * Gets the value of the desiredEffectCode property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the desiredEffectCode property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDesiredEffectCode().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DesiredEffectCodeType }
     */
    public List<DesiredEffectCodeType> getDesiredEffectCode() {
        if (desiredEffectCode == null) {
            desiredEffectCode = new ArrayList<DesiredEffectCodeType>();
        }
        return this.desiredEffectCode;
    }

    /**
     * 获取duration属性的值。
     *
     * @return possible object is
     * {@link DurationType }
     */
    public DurationType getDuration() {
        return duration;
    }

    /**
     * 设置duration属性的值。
     *
     * @param value allowed object is
     *              {@link DurationType }
     */
    public void setDuration(DurationType value) {
        this.duration = value;
    }

    /**
     * 获取endTime属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getEndTime() {
        return endTime;
    }

    /**
     * 设置endTime属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setEndTime(TimeInstantType value) {
        this.endTime = value;
    }

    /**
     * 获取performingEntity属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPerformingEntity() {
        return performingEntity;
    }

    /**
     * 设置performingEntity属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPerformingEntity(String value) {
        this.performingEntity = value;
    }

    /**
     * 获取startTime属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getStartTime() {
        return startTime;
    }

    /**
     * 设置startTime属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setStartTime(TimeInstantType value) {
        this.startTime = value;
    }

    /**
     * 获取taskActionCode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getTaskActionCode() {
        return taskActionCode;
    }

    /**
     * 设置taskActionCode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTaskActionCode(String value) {
        this.taskActionCode = value;
    }

    /**
     * Gets the value of the ruleOfEngagement property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ruleOfEngagement property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRuleOfEngagement().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RuleOfEngagementType }
     */
    public List<RuleOfEngagementType> getRuleOfEngagement() {
        if (ruleOfEngagement == null) {
            ruleOfEngagement = new ArrayList<RuleOfEngagementType>();
        }
        return this.ruleOfEngagement;
    }

    /**
     * Gets the value of the taskFunctionalRelation property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taskFunctionalRelation property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaskFunctionalRelation().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TaskFunctionalRelationType }
     */
    public List<TaskFunctionalRelationType> getTaskFunctionalRelation() {
        if (taskFunctionalRelation == null) {
            taskFunctionalRelation = new ArrayList<TaskFunctionalRelationType>();
        }
        return this.taskFunctionalRelation;
    }

    public List<BasicDataItem> getBasicDataItem() {
        if (BasicDataItem == null) {
            BasicDataItem = new ArrayList<BasicDataItem>();
        }
        return this.BasicDataItem;
    }
}
