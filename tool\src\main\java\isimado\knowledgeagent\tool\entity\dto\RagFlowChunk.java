package isimado.knowledgeagent.tool.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * DTO representing a content chunk from the retrieval API response
 */
public record RagFlowChunk(
    @JsonProperty("content")
    String content,

    @JsonProperty("content_ltks")
    String contentLtks,

    @JsonProperty("dataset_id")
    String datasetId,

    @JsonProperty("document_id")
    String documentId,

    @JsonProperty("document_keyword")
    String documentKeyword,

    @JsonProperty("highlight")
    String highlight,

    @JsonProperty("id")
    String id,

    @JsonProperty("image_id")
    String imageId,

    @JsonProperty("important_keywords")
    List<String> importantKeywords,

    @JsonProperty("positions")
    List<List<Integer>> positions,

    @JsonProperty("similarity")
    double similarity,

    @JsonProperty("term_similarity")
    double termSimilarity,

    @JsonProperty("vector_similarity")
    double vectorSimilarity
) {
}
