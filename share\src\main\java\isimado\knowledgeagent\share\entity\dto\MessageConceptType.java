//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#MessageConcept
 *
 * <p>MessageConceptType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="MessageConceptType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}C2SIMHeader"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Message"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}MessageBody"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}MessageCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReportContent"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}RequestContent"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MessageConceptType {

    @JsonProperty(value = "C2SIMHeader")
    protected C2SIMHeaderType c2SIMHeader;
    @JsonProperty(value = "Message")
    protected MessageType message;
    @JsonProperty(value = "MessageBody")
    protected MessageBodyType messageBody;
    @JsonProperty(value = "MessageCode")
    protected MessageCodeType messageCode;
    @JsonProperty(value = "ReportContent")
    protected ReportContentType reportContent;
    @JsonProperty(value = "RequestContent")
    protected RequestContentType requestContent;

    /**
     * 获取c2SIMHeader属性的值。
     *
     * @return possible object is
     * {@link C2SIMHeaderType }
     */
    public C2SIMHeaderType getC2SIMHeader() {
        return c2SIMHeader;
    }

    /**
     * 设置c2SIMHeader属性的值。
     *
     * @param value allowed object is
     *              {@link C2SIMHeaderType }
     */
    public void setC2SIMHeader(C2SIMHeaderType value) {
        this.c2SIMHeader = value;
    }

    /**
     * 获取message属性的值。
     *
     * @return possible object is
     * {@link MessageType }
     */
    public MessageType getMessage() {
        return message;
    }

    /**
     * 设置message属性的值。
     *
     * @param value allowed object is
     *              {@link MessageType }
     */
    public void setMessage(MessageType value) {
        this.message = value;
    }

    /**
     * 获取messageBody属性的值。
     *
     * @return possible object is
     * {@link MessageBodyType }
     */
    public MessageBodyType getMessageBody() {
        return messageBody;
    }

    /**
     * 设置messageBody属性的值。
     *
     * @param value allowed object is
     *              {@link MessageBodyType }
     */
    public void setMessageBody(MessageBodyType value) {
        this.messageBody = value;
    }

    /**
     * 获取messageCode属性的值。
     *
     * @return possible object is
     * {@link MessageCodeType }
     */
    public MessageCodeType getMessageCode() {
        return messageCode;
    }

    /**
     * 设置messageCode属性的值。
     *
     * @param value allowed object is
     *              {@link MessageCodeType }
     */
    public void setMessageCode(MessageCodeType value) {
        this.messageCode = value;
    }

    /**
     * 获取reportContent属性的值。
     *
     * @return possible object is
     * {@link ReportContentType }
     */
    public ReportContentType getReportContent() {
        return reportContent;
    }

    /**
     * 设置reportContent属性的值。
     *
     * @param value allowed object is
     *              {@link ReportContentType }
     */
    public void setReportContent(ReportContentType value) {
        this.reportContent = value;
    }

    /**
     * 获取requestContent属性的值。
     *
     * @return possible object is
     * {@link RequestContentType }
     */
    public RequestContentType getRequestContent() {
        return requestContent;
    }

    /**
     * 设置requestContent属性的值。
     *
     * @param value allowed object is
     *              {@link RequestContentType }
     */
    public void setRequestContent(RequestContentType value) {
        this.requestContent = value;
    }

}
