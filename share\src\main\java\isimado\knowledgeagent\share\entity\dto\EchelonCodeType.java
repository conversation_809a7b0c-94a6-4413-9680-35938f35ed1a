//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>EchelonCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="EchelonCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="AG"/>
 *     &lt;enumeration value="ARMY"/>
 *     &lt;enumeration value="BATGRP"/>
 *     &lt;enumeration value="BDE"/>
 *     &lt;enumeration value="BDEGRP"/>
 *     &lt;enumeration value="BN"/>
 *     &lt;enumeration value="BNG"/>
 *     &lt;enumeration value="CORPS"/>
 *     &lt;enumeration value="COY"/>
 *     &lt;enumeration value="COYG"/>
 *     &lt;enumeration value="DIV"/>
 *     &lt;enumeration value="FLEET"/>
 *     &lt;enumeration value="FLIGHT"/>
 *     &lt;enumeration value="NKN"/>
 *     &lt;enumeration value="NOS"/>
 *     &lt;enumeration value="NTF"/>
 *     &lt;enumeration value="NTG"/>
 *     &lt;enumeration value="NTU"/>
 *     &lt;enumeration value="PLT"/>
 *     &lt;enumeration value="REGION"/>
 *     &lt;enumeration value="RGT"/>
 *     &lt;enumeration value="SECT"/>
 *     &lt;enumeration value="SQDRNA"/>
 *     &lt;enumeration value="SQDRNM"/>
 *     &lt;enumeration value="SQUAD"/>
 *     &lt;enumeration value="TEAM"/>
 *     &lt;enumeration value="TSKELN"/>
 *     &lt;enumeration value="WING"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum EchelonCodeType {

    AG,
    ARMY,
    BATGRP,
    BDE,
    BDEGRP,
    BN,
    BNG,
    CORPS,
    COY,
    COYG,
    DIV,
    FLEET,
    FLIGHT,
    NKN,
    NOS,
    NTF,
    NTG,
    NTU,
    PLT,
    REGION,
    RGT,
    SECT,
    SQDRNA,
    SQDRNM,
    SQUAD,
    TEAM,
    TSKELN,
    WING;

    public static EchelonCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
