//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#EntityType
 *
 * <p>EntityTypeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="EntityTypeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}APP6-SIDC"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DISEntityType"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}NamedEntityType"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EntityTypeType {

    @JsonProperty(value = "APP6-SIDC")
    protected APP6SIDCType app6SIDC;
    @JsonProperty(value = "DISEntityType")
    protected DISEntityTypeType disEntityType;
    @JsonProperty(value = "NamedEntityType")
    protected NamedEntityTypeType namedEntityType;

    /**
     * 获取app6SIDC属性的值。
     *
     * @return possible object is
     * {@link APP6SIDCType }
     */
    public APP6SIDCType getAPP6SIDC() {
        return app6SIDC;
    }

    /**
     * 设置app6SIDC属性的值。
     *
     * @param value allowed object is
     *              {@link APP6SIDCType }
     */
    public void setAPP6SIDC(APP6SIDCType value) {
        this.app6SIDC = value;
    }

    /**
     * 获取disEntityType属性的值。
     *
     * @return possible object is
     * {@link DISEntityTypeType }
     */
    public DISEntityTypeType getDISEntityType() {
        return disEntityType;
    }

    /**
     * 设置disEntityType属性的值。
     *
     * @param value allowed object is
     *              {@link DISEntityTypeType }
     */
    public void setDISEntityType(DISEntityTypeType value) {
        this.disEntityType = value;
    }

    /**
     * 获取namedEntityType属性的值。
     *
     * @return possible object is
     * {@link NamedEntityTypeType }
     */
    public NamedEntityTypeType getNamedEntityType() {
        return namedEntityType;
    }

    /**
     * 设置namedEntityType属性的值。
     *
     * @param value allowed object is
     *              {@link NamedEntityTypeType }
     */
    public void setNamedEntityType(NamedEntityTypeType value) {
        this.namedEntityType = value;
    }

}
