//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/C2SIM#Event
 *
 * <p>EventType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="EventType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Duration" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EventCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}StartTime"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EventType implements Action {

    @JsonProperty(value = "ActionTemporalRelationship")
    protected List<ActionTemporalRelationshipType> actionTemporalRelationship;
    @JsonProperty(value = "Location")
    protected List<LocationType> location;
    @JsonProperty(value = "MapGraphicID")
    protected List<String> mapGraphicID;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "EventCode", required = true)
    protected EventCodeType eventCode;
    @JsonProperty(value = "StartTime", required = true)
    protected TimeInstantType startTime;
    @JsonProperty(value = "Describe")
    protected String describe;

    /**
     * Gets the value of the actionTemporalRelationship property.
     */
    public List<ActionTemporalRelationshipType> getActionTemporalRelationship() {
        if (actionTemporalRelationship == null) {
            actionTemporalRelationship = new ArrayList<ActionTemporalRelationshipType>();
        }
        return this.actionTemporalRelationship;
    }

    /**
     * Gets the value of the location property.
     */
    public List<LocationType> getLocation() {
        if (location == null) {
            location = new ArrayList<LocationType>();
        }
        return this.location;
    }

    /**
     * Gets the value of the mapGraphicID property.
     */
    public List<String> getMapGraphicID() {
        if (mapGraphicID == null) {
            mapGraphicID = new ArrayList<String>();
        }
        return this.mapGraphicID;
    }

    /**
     * 获取uuid属性的值。
     */
    public String getUUID() {
        return uuid;
    }
}
