//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#PhysicalEntity
 *
 * <p>PhysicalEntityType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PhysicalEntityType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CulturalFeature"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EnvironmentalObject"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}GeographicFeature"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}MapGraphic"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PhysicalEntityType {

    @JsonProperty(value = "CulturalFeature")
    protected CulturalFeatureType culturalFeature;
    @JsonProperty(value = "EnvironmentalObject")
    protected EnvironmentalObjectType environmentalObject;
    @JsonProperty(value = "GeographicFeature")
    protected GeographicFeatureType geographicFeature;
    @JsonProperty(value = "MapGraphic")
    protected MapGraphicType mapGraphic;

    /**
     * 获取culturalFeature属性的值。
     *
     * @return possible object is
     * {@link CulturalFeatureType }
     */
    public CulturalFeatureType getCulturalFeature() {
        return culturalFeature;
    }

    /**
     * 设置culturalFeature属性的值。
     *
     * @param value allowed object is
     *              {@link CulturalFeatureType }
     */
    public void setCulturalFeature(CulturalFeatureType value) {
        this.culturalFeature = value;
    }

    /**
     * 获取environmentalObject属性的值。
     *
     * @return possible object is
     * {@link EnvironmentalObjectType }
     */
    public EnvironmentalObjectType getEnvironmentalObject() {
        return environmentalObject;
    }

    /**
     * 设置environmentalObject属性的值。
     *
     * @param value allowed object is
     *              {@link EnvironmentalObjectType }
     */
    public void setEnvironmentalObject(EnvironmentalObjectType value) {
        this.environmentalObject = value;
    }

    /**
     * 获取geographicFeature属性的值。
     *
     * @return possible object is
     * {@link GeographicFeatureType }
     */
    public GeographicFeatureType getGeographicFeature() {
        return geographicFeature;
    }

    /**
     * 设置geographicFeature属性的值。
     *
     * @param value allowed object is
     *              {@link GeographicFeatureType }
     */
    public void setGeographicFeature(GeographicFeatureType value) {
        this.geographicFeature = value;
    }

    /**
     * 获取mapGraphic属性的值。
     *
     * @return possible object is
     * {@link MapGraphicType }
     */
    public MapGraphicType getMapGraphic() {
        return mapGraphic;
    }

    /**
     * 设置mapGraphic属性的值。
     *
     * @param value allowed object is
     *              {@link MapGraphicType }
     */
    public void setMapGraphic(MapGraphicType value) {
        this.mapGraphic = value;
    }

}
