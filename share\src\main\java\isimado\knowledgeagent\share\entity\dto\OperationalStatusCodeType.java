//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>OperationalStatusCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="OperationalStatusCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="FullyOperational"/>
 *     &lt;enumeration value="MostlyOperational"/>
 *     &lt;enumeration value="NotOperational"/>
 *     &lt;enumeration value="PartlyOperational"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum OperationalStatusCodeType {

    FULLY_OPERATIONAL("FullyOperational"),
    MOSTLY_OPERATIONAL("MostlyOperational"),
    NOT_OPERATIONAL("NotOperational"),
    PARTLY_OPERATIONAL("PartlyOperational");
    private final String value;

    OperationalStatusCodeType(String v) {
        value = v;
    }

    public static OperationalStatusCodeType fromValue(String v) {
        for (OperationalStatusCodeType c : OperationalStatusCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
