package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/1 10:00
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        visible = true,
        property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = VehicleType.class, name = "Vehicle"),
        @JsonSubTypes.Type(value = SurfaceVesselType.class, name = "SurfaceVessel"),
        @JsonSubTypes.Type(value = SubsurfaceVesselType.class, name = "SubsurfaceVessel"),
        @JsonSubTypes.Type(value = AircraftType.class, name = "Aircraft")
})
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public abstract class Platform implements ActorEntity {
    protected final String type;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "CurrentState")
    protected EntityStateType currentState;
    @JsonProperty(value = "Marking")
    protected String marking;
    @JsonProperty(value = "embedding")
    protected float[] embedding;
    @JsonProperty(value = "EntityDescriptor", required = true)
    protected EntityDescriptorType entityDescriptor;

    @JsonProperty(value = "EntityType", required = true)
    protected List<EntityTypeType> entityType;


    @Getter
    @Setter
    private double similarity;
    /**
     * Gets the value of the entityType property.
     */
    @Override
    public List<EntityTypeType> getEntityType() {
        if (entityType == null) {
            entityType = new ArrayList<EntityTypeType>();
        }
        return this.entityType;
    }

    @JsonIgnore
    public void setEntityType(DISEntityTypeType dis) {
        this.entityType = new ArrayList<>();
        EntityTypeType typeType = new EntityTypeType();
        typeType.setDISEntityType(dis);
        entityType.add(typeType);
    }

    protected Platform() {
        String string = this.getClass().getSimpleName();
        if (string.endsWith("Type")) {
            string = string.substring(0, string.length() - 4);

        }
        this.type = string;
    }
    /**
     * 设置entityDescriptor属性的值。
     *
     * @param value
     *     allowed object is
     *     {@link EntityDescriptorType }
     *
     */
    /**
     * 设置entityDescriptor属性的值。
     *
     * @param value allowed object is
     *              {@link EntityDescriptorType }
     */
    public void setEntityDescriptor(EntityDescriptorType value) {
        this.entityDescriptor = value;
    }

    public String getName() {
        return this.name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    public String getUUID() {
        return this.uuid;
    }

    /**
     * 设置uuid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUUID(String value) {
        this.uuid = value;
    }

    public EntityStateType getCurrentState() {
        return currentState;
    }

    /**
     * 设置currentState属性的值。
     *
     * @param value allowed object is
     *              {@link EntityStateType }
     */
    public void setCurrentState(EntityStateType value) {
        this.currentState = value;
    }

    /**
     * 获取marking属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMarking() {
        return marking;
    }

    /**
     * 设置marking属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMarking(String value) {
        this.marking = value;
    }

    /**
     * 获取向量嵌入
     *
     * @return
     */
    public float[] getEmbedding() {
        return embedding;
    }

    /**
     * 向量嵌入
     *
     * @param embed
     */
    public void setEmbedding(float[] embed) {
        this.embedding = embed;
    }


}
