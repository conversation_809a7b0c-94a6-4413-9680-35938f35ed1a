//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/C2SIM#RequestBody
 *
 * <p>RequestBodyType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="RequestBodyType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DomainMessageBodyGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}RequestContent" maxOccurs="unbounded"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}RequestingEntity"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class RequestBodyType {

    @JsonProperty(value = "FromSender", required = true)
    protected String fromSender;
    @JsonProperty(value = "ToReceiver", required = true)
    protected String toReceiver;
    @JsonProperty(value = "RequestContent", required = true)
    protected List<RequestContentType> requestContent;
    @JsonProperty(value = "RequestingEntity", required = true)
    protected String requestingEntity;

    /**
     * 获取fromSender属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFromSender() {
        return fromSender;
    }

    /**
     * 设置fromSender属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFromSender(String value) {
        this.fromSender = value;
    }

    /**
     * 获取toReceiver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getToReceiver() {
        return toReceiver;
    }

    /**
     * 设置toReceiver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setToReceiver(String value) {
        this.toReceiver = value;
    }

    /**
     * Gets the value of the requestContent property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the requestContent property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRequestContent().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link RequestContentType }
     */
    public List<RequestContentType> getRequestContent() {
        if (requestContent == null) {
            requestContent = new ArrayList<RequestContentType>();
        }
        return this.requestContent;
    }

    /**
     * 获取requestingEntity属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRequestingEntity() {
        return requestingEntity;
    }

    /**
     * 设置requestingEntity属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRequestingEntity(String value) {
        this.requestingEntity = value;
    }

}
