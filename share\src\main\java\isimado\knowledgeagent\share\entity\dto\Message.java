package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/2 14:28
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class Message {

    @Getter
    private MessageType messageType;
    private List<C2SIMObject> objectList;
    private Map<String, List<C2SIMObject>> objectMap;
    private Map<String, UnitSymbolType> unitSymbolMap;

    public Message(MessageType messageType) {
        this.messageType = messageType;
    }

    /**
     * --------------------扩展方法-----------------
     */

    public MessageBodyType getMessageBody() {
        return messageType.getMessageBody();
    }

    public C2SIMHeaderType getC2SIMHeader() {
        return messageType.getC2SIMHeader();
    }


}
