//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>TaskFunctionalAssociationCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="TaskFunctionalAssociationCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ALT"/>
 *     &lt;enumeration value="HASPRV"/>
 *     &lt;enumeration value="HASSEC"/>
 *     &lt;enumeration value="HSA"/>
 *     &lt;enumeration value="IMO"/>
 *     &lt;enumeration value="INRSTO"/>
 *     &lt;enumeration value="IOT"/>
 *     &lt;enumeration value="ISAPRQ"/>
 *     &lt;enumeration value="ISCAUS"/>
 *     &lt;enumeration value="TPL"/>
 *     &lt;enumeration value="UAR"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum TaskFunctionalAssociationCodeType {

    ALT,
    HASPRV,
    HASSEC,
    HSA,
    IMO,
    INRSTO,
    IOT,
    ISAPRQ,
    ISCAUS,
    TPL,
    UAR;

    public static TaskFunctionalAssociationCodeType fromValue(String v) {
        return valueOf(v);
    }

    public String value() {
        return name();
    }

}
