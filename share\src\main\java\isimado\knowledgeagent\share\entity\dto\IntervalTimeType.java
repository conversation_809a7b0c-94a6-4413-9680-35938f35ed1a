//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#IntervalTime
 *
 * <p>IntervalTimeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="IntervalTimeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Duration" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EndTime" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}StartTime" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class IntervalTimeType {

    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "EndTime")
    protected TimeInstantType endTime;
    @JsonProperty(value = "StartTime")
    protected TimeInstantType startTime;

    /**
     * 获取duration属性的值。
     *
     * @return possible object is
     * {@link DurationType }
     */
    public DurationType getDuration() {
        return duration;
    }

    /**
     * 设置duration属性的值。
     *
     * @param value allowed object is
     *              {@link DurationType }
     */
    public void setDuration(DurationType value) {
        this.duration = value;
    }

    /**
     * 获取endTime属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getEndTime() {
        return endTime;
    }

    /**
     * 设置endTime属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setEndTime(TimeInstantType value) {
        this.endTime = value;
    }

    /**
     * 获取startTime属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getStartTime() {
        return startTime;
    }

    /**
     * 设置startTime属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setStartTime(TimeInstantType value) {
        this.startTime = value;
    }

}
