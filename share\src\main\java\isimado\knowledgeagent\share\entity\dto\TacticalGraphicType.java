//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/smx#TacticalGraphic
 *
 * <p>TacticalGraphicType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="TacticalGraphicType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Line"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}NBC_Event"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Point"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TacticalArea"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskGraphic"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class TacticalGraphicType {

    @JsonProperty(value = "Line")
    protected LineType line;
    @JsonProperty(value = "NBC_Event")
    protected NBCEventType nbcEvent;
    @JsonProperty(value = "Point")
    protected PointType point;
    @JsonProperty(value = "TacticalArea")
    protected TacticalAreaType tacticalArea;
    @JsonProperty(value = "TaskGraphic")
    protected TaskGraphicType taskGraphic;

    /**
     * 获取line属性的值。
     *
     * @return possible object is
     * {@link LineType }
     */
    public LineType getLine() {
        return line;
    }

    /**
     * 设置line属性的值。
     *
     * @param value allowed object is
     *              {@link LineType }
     */
    public void setLine(LineType value) {
        this.line = value;
    }

    /**
     * 获取nbcEvent属性的值。
     *
     * @return possible object is
     * {@link NBCEventType }
     */
    public NBCEventType getNBCEvent() {
        return nbcEvent;
    }

    /**
     * 设置nbcEvent属性的值。
     *
     * @param value allowed object is
     *              {@link NBCEventType }
     */
    public void setNBCEvent(NBCEventType value) {
        this.nbcEvent = value;
    }

    /**
     * 获取point属性的值。
     *
     * @return possible object is
     * {@link PointType }
     */
    public PointType getPoint() {
        return point;
    }

    /**
     * 设置point属性的值。
     *
     * @param value allowed object is
     *              {@link PointType }
     */
    public void setPoint(PointType value) {
        this.point = value;
    }

    /**
     * 获取tacticalArea属性的值。
     *
     * @return possible object is
     * {@link TacticalAreaType }
     */
    public TacticalAreaType getTacticalArea() {
        return tacticalArea;
    }

    /**
     * 设置tacticalArea属性的值。
     *
     * @param value allowed object is
     *              {@link TacticalAreaType }
     */
    public void setTacticalArea(TacticalAreaType value) {
        this.tacticalArea = value;
    }

    /**
     * 获取taskGraphic属性的值。
     *
     * @return possible object is
     * {@link TaskGraphicType }
     */
    public TaskGraphicType getTaskGraphic() {
        return taskGraphic;
    }

    /**
     * 设置taskGraphic属性的值。
     *
     * @param value allowed object is
     *              {@link TaskGraphicType }
     */
    public void setTaskGraphic(TaskGraphicType value) {
        this.taskGraphic = value;
    }

}
