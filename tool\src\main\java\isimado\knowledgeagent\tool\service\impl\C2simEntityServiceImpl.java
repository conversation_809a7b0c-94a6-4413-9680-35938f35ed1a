package isimado.knowledgeagent.tool.service.impl;


import com.fasterxml.jackson.core.type.TypeReference;
import isimado.framework.util.JsonUtils;
import isimado.knowledgeagent.share.entity.dto.DISEntityTypeType;
import isimado.knowledgeagent.share.entity.dto.Platform;
import isimado.knowledgeagent.tool.entity.dao.C2simEntity;
import isimado.knowledgeagent.tool.entity.dao.EntityType;
import isimado.knowledgeagent.tool.entity.dto.C2simXmlValidResultDTO;
import isimado.knowledgeagent.tool.entity.dto.EquipInfo;
import isimado.knowledgeagent.tool.mapper.C2simEntityMapper;
import isimado.knowledgeagent.tool.mapper.EntityTypeMapper;
import isimado.knowledgeagent.tool.service.C2simEntityService;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import javax.xml.XMLConstants;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.IOException;
import java.io.StringReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.baomidou.mybatisplus.core.toolkit.Wrappers.lambdaQuery;
import static java.util.Collections.emptyList;
import static org.springframework.util.CollectionUtils.isEmpty;

@Slf4j
@Service
public class C2simEntityServiceImpl implements C2simEntityService {


    @Override
    @Tool(name = "validateC2simEntity", description = "验证 C2SIM JSON是否合法")
    public void validateC2simEntity(String c2simJsonString) {

        log.info("校验字段: {}", c2simJsonString);

        Map parse = JsonUtils.parse(c2simJsonString, Map.class);

        // 1. 解析 JSON 字符串为 C2SIM 实体对象
        try {
            if (parse.size() == 1) {
                Map<String, Platform> r = JsonUtils.parse(c2simJsonString, new TypeReference<>() {
                });
            } else {
                JsonUtils.parse(c2simJsonString, Platform.class);
            }

            log.info("验证成功.");
        } catch (Exception e) {
            log.error("初步验证失败: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }


    @Override
    @Tool(name = "validateC2simXml", description = "验证 C2SIM xml 是否合法")
    @SneakyThrows
    public C2simXmlValidResultDTO validateC2simXml(String xmlString) {
        xmlString = xmlString.trim();
        List<String> errorCollector = new ArrayList<>();
        Validator validator = getValidator(errorCollector);
        validator.validate(new StreamSource(new StringReader(xmlString)));

        if (errorCollector.size() <= 5) {
            return new C2simXmlValidResultDTO(true, emptyList());
        }
        return new C2simXmlValidResultDTO(isEmpty(errorCollector), errorCollector);
    }

    @SneakyThrows
    private Validator getValidator(List<String> errorCollector) {
        SchemaFactory factory = SchemaFactory.newInstance(
                XMLConstants.W3C_XML_SCHEMA_NS_URI
        );

        Source xsdSource = new StreamSource(this.getClass().getClassLoader().getResourceAsStream("c2sim.xsd"));
        Schema schema = factory.newSchema(xsdSource);

        Validator validator = schema.newValidator();

        // 设置自定义错误处理器（可选）
        validator.setErrorHandler(new ErrorHandler() {
            @Override
            public void warning(SAXParseException e) {
                log.warn("xml验证： {}", e.getMessage());
            }

            @Override
            public void error(SAXParseException e) {
                log.error("xml验证： {}", e.getMessage());
                errorCollector.add(e.getMessage());
            }

            @Override
            public void fatalError(SAXParseException e) throws SAXException {
                log.error("致命错误： {}", e.getMessage());
                errorCollector.add(e.getMessage());
            }
        });
        return validator;
    }



    @Resource
    private C2simEntityMapper entityMapper;
    @Resource
    private EmbeddingModel embeddingModel;
    @Resource
    private ChatClient chatClient;
    @Resource
    private EntityTypeMapper entityTypeMapper;


    @Override
    public void saveC2simEntity(Platform platform) {

        String platformType = platform.getClass().getSimpleName();
        if (platformType.endsWith("Type")) {
            platformType = platformType.substring(0, platformType.length() - 4);
        }

        long count = entityMapper.selectCount(lambdaQuery(C2simEntity.class)
                .eq(C2simEntity::getName, platform.getName())
                .eq(C2simEntity::getType, platformType));

        if (count > 0) {
            return;
        }

        getDisEntityType(platform, platformType);

        C2simEntity entity = new C2simEntity();
        entity.setName(platform.getName());
        entity.setType(platformType);
        entity.setContent(JsonUtils.toJson(platform));

        entity.setEmbedding(embeddingModel.embed(platformType + "," + entity.getName()));


        entityMapper.insert(entity);
    }

    private DISEntityTypeType getDisEntityType(Platform platform, String platformType) {
        String text = platform.getName() + "," + platformType;
        float[] embed = embeddingModel.embed(text);

        List<EntityType> list = entityTypeMapper.vectorRetrieval(JsonUtils.toJson(embed));

        if (list.size() > 0) {
            EntityType type = list.get(0);
            if (type.getSimilarity() > 0.8) {
                DISEntityTypeType disEntityType = new DISEntityTypeType();
                disEntityType.setDISKind(Double.valueOf(type.getKind()).intValue());
                disEntityType.setDISDomain(Double.valueOf(type.getDomain()).intValue());
                disEntityType.setDISCountry(Double.valueOf(type.getCountry()).intValue());
                disEntityType.setDISCategory(Double.valueOf(type.getCategory()).intValue());
                disEntityType.setDISSubCategory(Double.valueOf(type.getSubcategory()).intValue());
                disEntityType.setDISSpecific(Double.valueOf(type.getSpecific()).intValue());
                disEntityType.setDISExtra(Double.valueOf(type.getExtra()).intValue());

                platform.setEntityType(disEntityType);
                return disEntityType;
            }
        }
        return null;
    }

    @Override
    public List<Platform> searchC2simEntity(int limit, String text, double minSimilarity) {
        EquipInfo equipName = getFullName(text);
        text = String.join(",", equipName.equipType(), equipName.name());

        float[] embed = embeddingModel.embed(text);

        List<C2simEntity> entities = entityMapper.vectorRetrieval(JsonUtils.toJson(embed), limit);

        return entities.stream().filter(e -> {
                    System.out.println(e.getSimilarity());
                    return e.getSimilarity() >= minSimilarity;
                }).map(e -> {
                    Platform platform = JsonUtils.parse(e.getContent(), Platform.class);
                    platform.setSimilarity(e.getSimilarity());
                    return platform;
                })
                .toList();
    }

    /**
     * 获取完整的名称
     *
     * @param name
     * @return
     */
    private EquipInfo getFullName(String name) {
        UserMessage userMessage = new UserMessage(name);
        SystemMessage systemMessage = new SystemMessage("""
                提取其中的武器装备名称，搜索获取它的官方名称(如果是中文，还需翻译成对应的英文名称),确定它的类型枚举，枚举项包含(Vehicle/Aircraft/SurfaceVessel/SubsurfaceVessel)。
                以JSON结构输出，如下
                {
                "name":"装备1",
                "officialName":"装备1官方名称",
                "equipType":"装备类型"
                }
                """);
        EquipInfo info = chatClient.prompt(new Prompt(userMessage, systemMessage))
                .call()
                .entity(EquipInfo.class);

        return info;
    }

}

