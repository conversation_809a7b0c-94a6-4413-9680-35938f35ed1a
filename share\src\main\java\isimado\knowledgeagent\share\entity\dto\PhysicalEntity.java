package isimado.knowledgeagent.share.entity.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/1 9:56
 */
public interface PhysicalEntity extends Entity {

    /**
     * 获取marking属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    String getMarking();

    String getOriginID();

    void setOriginID(String value);

    List<BasicDataItem> getBasicDataItem();

    void setBasicDataItem(List<BasicDataItem> basicDataItem);

}
