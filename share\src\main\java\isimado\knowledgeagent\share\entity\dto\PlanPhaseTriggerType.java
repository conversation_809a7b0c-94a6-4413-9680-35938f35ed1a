//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/lox#PlanPhaseTrigger
 *
 * <p>PlanPhaseTriggerType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PlanPhaseTriggerType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EventTrigger"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OnOrderTrigger"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PriorPhaseCompletionTrigger"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PlanPhaseTriggerType {

    @JsonProperty(value = "EventTrigger")
    protected EventTriggerType eventTrigger;
    @JsonProperty(value = "OnOrderTrigger")
    protected OnOrderTriggerType onOrderTrigger;
    @JsonProperty(value = "PriorPhaseCompletionTrigger")
    protected PriorPhaseCompletionTriggerType priorPhaseCompletionTrigger;

    /**
     * 获取eventTrigger属性的值。
     *
     * @return possible object is
     * {@link EventTriggerType }
     */
    public EventTriggerType getEventTrigger() {
        return eventTrigger;
    }

    /**
     * 设置eventTrigger属性的值。
     *
     * @param value allowed object is
     *              {@link EventTriggerType }
     */
    public void setEventTrigger(EventTriggerType value) {
        this.eventTrigger = value;
    }

    /**
     * 获取onOrderTrigger属性的值。
     *
     * @return possible object is
     * {@link OnOrderTriggerType }
     */
    public OnOrderTriggerType getOnOrderTrigger() {
        return onOrderTrigger;
    }

    /**
     * 设置onOrderTrigger属性的值。
     *
     * @param value allowed object is
     *              {@link OnOrderTriggerType }
     */
    public void setOnOrderTrigger(OnOrderTriggerType value) {
        this.onOrderTrigger = value;
    }

    /**
     * 获取priorPhaseCompletionTrigger属性的值。
     *
     * @return possible object is
     * {@link PriorPhaseCompletionTriggerType }
     */
    public PriorPhaseCompletionTriggerType getPriorPhaseCompletionTrigger() {
        return priorPhaseCompletionTrigger;
    }

    /**
     * 设置priorPhaseCompletionTrigger属性的值。
     *
     * @param value allowed object is
     *              {@link PriorPhaseCompletionTriggerType }
     */
    public void setPriorPhaseCompletionTrigger(PriorPhaseCompletionTriggerType value) {
        this.priorPhaseCompletionTrigger = value;
    }

}
