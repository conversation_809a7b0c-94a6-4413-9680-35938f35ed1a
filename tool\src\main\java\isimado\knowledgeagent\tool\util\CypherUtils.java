package isimado.knowledgeagent.tool.util;

import isimado.knowledgeagent.share.entity.dto.Platform;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CypherUtils {


    public static String createCypherQuery(List<String> labels, Platform entity) {
        Map<String, Object> properties = JsonUtils.fromJson(JsonUtils.toJson(entity), Map.class);
        return "CREATE (e" + String.join("", labels.stream().map(l -> ":" + l).toList())
                + " " + formatCypherJSON(properties) + ")";
    }


    private static String formatCypherJSON(Object object) {
        if (object == null) {
            return "null";
        }
        if (object instanceof Map<?, ?> map) {
            List<String> list = map.entrySet().stream()
                    .filter(e -> e.getValue() != null)
                    .map(entry -> entry.getKey() + ": " + formatCypherJSON(entry.getValue()))
                    .toList();
            return "{" + String.join(",", list) + "}";
        } else if (object instanceof String string) {
            return "\"" + string + "\"";
        } else if (object instanceof List list) {
            return "[" + String.join(",", list.stream().map(CypherUtils::formatCypherJSON).toList()) + "]";
        } else if (object.getClass().isArray()) {
            List list = new ArrayList<>();
            int length = Array.getLength(object);
            for (int i = 0; i < length; i++) {
                list.add(Array.get(object, i));
            }
            return formatCypherJSON(list);
        }
        return object.toString();
    }

}
