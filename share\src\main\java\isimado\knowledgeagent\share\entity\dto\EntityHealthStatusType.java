//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#EntityHealthStatus
 *
 * <p>EntityHealthStatusType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="EntityHealthStatusType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OperationalStatus"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Resources"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Strength"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class EntityHealthStatusType {

    @JsonProperty(value = "OperationalStatus")
    protected OperationalStatusType operationalStatus;
    @JsonProperty(value = "Resources")
    protected ResourcesType resources;
    @JsonProperty(value = "Strength")
    protected StrengthType strength;

    /**
     * 获取operationalStatus属性的值。
     *
     * @return possible object is
     * {@link OperationalStatusType }
     */
    public OperationalStatusType getOperationalStatus() {
        return operationalStatus;
    }

    /**
     * 设置operationalStatus属性的值。
     *
     * @param value allowed object is
     *              {@link OperationalStatusType }
     */
    public void setOperationalStatus(OperationalStatusType value) {
        this.operationalStatus = value;
    }

    /**
     * 获取resources属性的值。
     *
     * @return possible object is
     * {@link ResourcesType }
     */
    public ResourcesType getResources() {
        return resources;
    }

    /**
     * 设置resources属性的值。
     *
     * @param value allowed object is
     *              {@link ResourcesType }
     */
    public void setResources(ResourcesType value) {
        this.resources = value;
    }

    /**
     * 获取strength属性的值。
     *
     * @return possible object is
     * {@link StrengthType }
     */
    public StrengthType getStrength() {
        return strength;
    }

    /**
     * 设置strength属性的值。
     *
     * @param value allowed object is
     *              {@link StrengthType }
     */
    public void setStrength(StrengthType value) {
        this.strength = value;
    }

}
