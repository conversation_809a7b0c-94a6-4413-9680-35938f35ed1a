<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="isimado.knowledgeagent.tool.mapper.C2simEntityMapper">


    <select id="vectorRetrieval" resultType="isimado.knowledgeagent.tool.entity.dao.C2simEntity">
        select *, 1 - (embedding <![CDATA[ <=> ]]> '${embeddingString}') as similarity
        from c2sim_entity
        ORDER BY similarity DESC LIMIT #{limit};
    </select>
</mapper>
