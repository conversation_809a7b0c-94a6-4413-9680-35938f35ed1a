package isimado.knowledgeagent.tool.service.impl;

import isimado.knowledgeagent.tool.entity.dto.DocumentRetrieval;
import isimado.knowledgeagent.tool.entity.dto.RagFlowResponse;
import isimado.knowledgeagent.tool.service.DocumentRetrievalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.util.List;


@Service
@Slf4j
public class DocumentRetrievalServiceImpl implements DocumentRetrievalService , InitializingBean {
    RestClient restClient;
    @Override
    public void afterPropertiesSet() throws Exception {
        restClient = RestClient.builder()
                .baseUrl("http://localhost")
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer ragflow-FhMGUzZDU4MjMzYzExZjBiNjIwNDZjNW")
                .build();
    }


    @Override
    @Tool(name = "retrievalDocument", description = "根据文本查询检索相关文档")
    public RagFlowResponse retrievalDocument(@ToolParam(description = "文本") String text) {
        log.info("检索文档: {}", text);
        DocumentRetrieval retrieval = new DocumentRetrieval(text,
                List.of("c02e173c21af11f0b1dbfa75bd6b0f51"));
        RagFlowResponse response = restClient.post()
                .uri("/api/v1/retrieval")
                .body(retrieval)
                .retrieve()
                .body(RagFlowResponse.class);
        log.info("响应值: {}", response.data().chunks().size());
        return response;
    }
}
