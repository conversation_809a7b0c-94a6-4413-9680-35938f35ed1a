package isimado.knowledgeagent.tool.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class to convert Excel file data to SQLite database
 */
public class ExcelToSqliteConverter {

    private static final Logger log = LoggerFactory.getLogger(ExcelToSqliteConverter.class);
    private static final String DB_NAME = "siso_enumerations.db";
    /**
     * Main method to execute the conversion process
     *
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        // Look for the Excel file in the current directory and parent directory
        String excelFileName = "SISO-REF-010-2024 Enumerations v34.xlsx";
        File excelFile = new File(excelFileName);

        if (!excelFile.exists()) {
            // Try the parent directory
            excelFile = new File("..", excelFileName);
            if (!excelFile.exists()) {
                log.error("Excel file not found: {}", excelFileName);
                return;
            }
        }

        try {
            ExcelToSqliteConverter converter = new ExcelToSqliteConverter();
            log.info("Using Excel file: {}", excelFile.getAbsolutePath());
            converter.convertExcelToSqlite(excelFile.getAbsolutePath());
            log.info("Conversion completed successfully!");
        } catch (Exception e) {
            log.error("Error during conversion: {}", e.getMessage(), e);
        }
    }

    private static final Pattern INVALID_CHARS = Pattern.compile("[^a-zA-Z0-9_]");

    /**
     * Convert Excel file to SQLite database
     *
     * @param excelFilePath Path to the Excel file
     * @throws IOException If there's an error reading the Excel file
     * @throws SQLException If there's an error with the database operations
     */
    @SneakyThrows
    public void convertExcelToSqlite(String excelFilePath) throws IOException, SQLException {
        // Determine the database path (in the same directory as the Excel file)
        File excelFile = new File(excelFilePath);
        File dbFile = new File(excelFile.getParentFile(), DB_NAME);
        String dbPath = dbFile.getAbsolutePath();

        log.info("Creating SQLite database at: {}", dbPath);

        // Create a new database file or connect to existing one
        try (Connection connection = DriverManager.getConnection("jdbc:sqlite:" + dbPath)) {
            log.info("Connected to SQLite database");
            AtomicInteger count = new AtomicInteger(0);
            // Read the Excel file
            try (FileInputStream fis = new FileInputStream(excelFile);
                 Workbook workbook = new XSSFWorkbook(fis)) {


                // Process each sheet in the workbook
                for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    String sheetName = sheet.getSheetName();
                    if (isNumber(sheetName)) {
                        count.incrementAndGet();
                        Thread.startVirtualThread(() -> {
                            try {
                                processSheet(connection, sheet);
                            } catch (SQLException e) {
                                log.error("报错: {}", sheetName, e);
                                System.exit(1);
                            } finally {
                                count.decrementAndGet();
                            }
                        });

                    }

                }

                while (true) {
                    Thread.sleep(5000);
                    log.info("剩余: {}", count.get());
                }
            }
        }
    }

    static boolean isNumber(String str) {
        return Pattern.compile("[0-9]+?").matcher(str).matches();
    }

    /**
     * Process a single sheet from the Excel workbook
     *
     * @param connection Database connection
     * @param sheet Excel sheet to process
     * @throws SQLException If there's an error with the database operations
     */
    private void processSheet(Connection connection, Sheet sheet) throws SQLException {


        // Get the first row that contains the table name
        Row titleRow = null;
        int titleRowIndex = -1;

        Row row = sheet.getRow(0);
        Cell cell = row.getCell(0);
        String cellValue = getCellValueAsString(cell);
        int index = cellValue.indexOf("[");
        String tableName = cellValue.substring(0, index);




        // Sanitize table name (remove special characters)
        tableName = sanitizeTableName(tableName);

        Row headerRow = sheet.getRow(1);


        // Extract column names from the header row
        List<String> columnNames = new ArrayList<>();
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            String columnName = getCellValueAsString(headerRow.getCell(i));
            if (columnName != null && !columnName.trim().isEmpty()) {
                columnNames.add(sanitizeColumnName(columnName));
            }
        }

        if (columnNames.isEmpty()) {
            log.warn("Sheet '{}' has no valid column headers, skipping", sheet.getSheetName());
            return;
        }

        // Create the table
        createTable(connection, tableName, columnNames);

        // Insert data (starting from the row after the header row)
        int rows = insertData(connection, sheet, tableName, columnNames, 2);

        log.info("表：{} 插入 {} 条数", tableName, rows);
    }

    /**
     * Create a table in the SQLite database
     *
     * @param connection Database connection
     * @param tableName Name of the table to create
     * @param columnNames List of column names
     * @throws SQLException If there's an error with the database operations
     */
    private void createTable(Connection connection, String tableName, List<String> columnNames) throws SQLException {
        // Drop table if it exists
        try (Statement stmt = connection.createStatement()) {
            stmt.execute("DROP TABLE IF EXISTS " + tableName);
        }

        // Create the table
        StringBuilder createTableSQL = new StringBuilder("CREATE TABLE " + tableName + " (");
        createTableSQL.append("id INTEGER PRIMARY KEY AUTOINCREMENT");

        for (String columnName : columnNames) {
            createTableSQL.append(", ").append(columnName).append(" TEXT");
        }

        createTableSQL.append(")");

        try (Statement stmt = connection.createStatement()) {
            try {
                stmt.execute(createTableSQL.toString());
            } catch (Exception e) {
                log.error("表创建失败: {}", createTableSQL, e);
                throw e;
            }

        }
    }

    /**
     * Insert data from the Excel sheet into the SQLite table
     *
     * @param connection Database connection
     * @param sheet Excel sheet containing the data
     * @param tableName Name of the table to insert data into
     * @param columnNames List of column names
     * @param dataStartRow The row index to start reading data from
     * @throws SQLException If there's an error with the database operations
     */
    private int insertData(Connection connection, Sheet sheet, String tableName, List<String> columnNames, int dataStartRow) throws SQLException {
        // Prepare the insert statement
        StringBuilder insertSQL = new StringBuilder("INSERT INTO " + tableName + " (");
        insertSQL.append(String.join(", ", columnNames));
        insertSQL.append(") VALUES (");

        for (int i = 0; i < columnNames.size(); i++) {
            insertSQL.append(i == 0 ? "?" : ", ?");
        }

        insertSQL.append(")");

        int rowCount = 0;

        try (PreparedStatement pstmt = connection.prepareStatement(insertSQL.toString())) {
            for (int rowIndex = dataStartRow; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;

                // Check if the row has any data
                boolean hasData = false;
                for (int i = 0; i < columnNames.size() && i < row.getLastCellNum(); i++) {
                    String cellValue = getCellValueAsString(row.getCell(i));
                    if (cellValue != null && !cellValue.trim().isEmpty()) {
                        hasData = true;
                        break;
                    }
                }

                if (!hasData) continue;

                // Set values for the prepared statement
                for (int i = 0; i < columnNames.size(); i++) {
                    String cellValue = "";
                    if (i < row.getLastCellNum()) {
                        cellValue = getCellValueAsString(row.getCell(i));
                    }
                    pstmt.setString(i + 1, cellValue);
                }

                pstmt.executeUpdate();
                rowCount++;
            }
        }

        return rowCount;

    }

    /**
     * Get cell value as string, handling different cell types
     *
     * @param cell Excel cell
     * @return String representation of the cell value
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    yield cell.getDateCellValue().toString();
                } else {
                    // Convert to string to avoid scientific notation
                    yield String.valueOf(cell.getNumericCellValue());
                }
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> {
                try {
                    yield cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        yield String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        yield "";
                    }
                }
            }
            default -> "";
        };
    }

    /**
     * Sanitize table name by removing special characters
     *
     * @param tableName Original table name
     * @return Sanitized table name
     */
    private String sanitizeTableName(String tableName) {
        tableName = tableName.trim();
        // Replace spaces and special characters with underscores
        String sanitized = INVALID_CHARS.matcher(tableName).replaceAll("_");


        return sanitized.toLowerCase();
    }

    /**
     * Sanitize column name by removing special characters
     *
     * @param columnName Original column name
     * @return Sanitized column name
     */
    private String sanitizeColumnName(String columnName) {
        // Replace spaces and special characters with underscores
        String sanitized = INVALID_CHARS.matcher(columnName).replaceAll("_");

        sanitized = sanitized.toLowerCase();
        if (sanitized.equalsIgnoreCase("group")) {
            sanitized = "'group'";
        }
        return sanitized;
    }
}
