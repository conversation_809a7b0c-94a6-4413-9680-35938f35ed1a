//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.07.05 时间 04:23:24 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/lox#PlanBody
 *
 * <p>PlanBodyType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PlanBodyType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DomainMessageBodyGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhase" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PlanPhaseReference" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ToBeExecutedNow"/>
 *         &lt;element name="PlanID" type="{http://www.sisostds.org/schemas/C2SIM/1.1}UUIDType"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Name" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PlanBodyType {

    @JsonProperty(value = "FromSender", required = true)
    protected String fromSender;
    @JsonProperty(value = "ToReceiver", required = true)
    protected String toReceiver;
    @JsonProperty(value = "PlanPhase")
    protected List<PlanPhaseType> planPhase;
    @JsonProperty(value = "PlanPhaseReference")
    protected List<String> planPhaseReference;
    @JsonProperty(value = "ToBeExecutedNow")
    protected boolean toBeExecutedNow;
    @JsonProperty(value = "PlanID", required = true)
    protected String planID;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "Sort")
    protected Integer sort;

    /**
     * 获取fromSender属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFromSender() {
        return fromSender;
    }

    /**
     * 设置fromSender属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFromSender(String value) {
        this.fromSender = value;
    }

    /**
     * 获取toReceiver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getToReceiver() {
        return toReceiver;
    }

    /**
     * 设置toReceiver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setToReceiver(String value) {
        this.toReceiver = value;
    }

    /**
     * Gets the value of the planPhase property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the planPhase property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPlanPhase().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PlanPhaseType }
     */
    public List<PlanPhaseType> getPlanPhase() {
        if (planPhase == null) {
            planPhase = new ArrayList<PlanPhaseType>();
        }
        return this.planPhase;
    }

    /**
     * Gets the value of the planPhaseReference property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the planPhaseReference property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPlanPhaseReference().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     */
    public List<String> getPlanPhaseReference() {
        if (planPhaseReference == null) {
            planPhaseReference = new ArrayList<String>();
        }
        return this.planPhaseReference;
    }

    /**
     * 获取toBeExecutedNow属性的值。
     */
    public boolean isToBeExecutedNow() {
        return toBeExecutedNow;
    }

    /**
     * 设置toBeExecutedNow属性的值。
     */
    public void setToBeExecutedNow(boolean value) {
        this.toBeExecutedNow = value;
    }

    /**
     * 获取planID属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPlanID() {
        return planID;
    }

    /**
     * 设置planID属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPlanID(String value) {
        this.planID = value;
    }

    /**
     * 获取name属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
