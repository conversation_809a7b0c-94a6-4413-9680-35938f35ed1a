package isimado.knowledgeagent.tool.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ExcelReader {

    public static List<List<String>> readExcel(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new FileNotFoundException("Excel file not found: " + filePath);
        }
        if (!filePath.toLowerCase().endsWith(".xlsx")) {
            throw new IllegalArgumentException("Only .xlsx files are supported");
        }

        List<List<String>> data = new ArrayList<>();

        try (FileInputStream fis = new FileInputStream(file);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheetAt(0); // 读取第一个工作表

            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    switch (cell.getCellType()) {
                        case STRING:
                            rowData.add(cell.getStringCellValue());
                            break;
                        case NUMERIC:
                            rowData.add(String.valueOf(cell.getNumericCellValue()));
                            break;
                        case BOOLEAN:
                            rowData.add(String.valueOf(cell.getBooleanCellValue()));
                            break;
                        default:
                            rowData.add("");
                    }
                }
                data.add(rowData);
            }
        }

        return data;
    }

    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("Usage: java ExcelReader <path_to_xlsx_file>");
            System.out.println("Example: java ExcelReader data/sample.xlsx");
            return;
        }

        try {
            String filePath = args[0];
            System.out.println("Reading Excel file: " + filePath);
            
            List<List<String>> excelData = readExcel(filePath);
            
            System.out.println("\nExcel Data:");
            for (List<String> row : excelData) {
                System.out.println("| " + String.join(" | ", row) + " |");
            }
            System.out.println("\nTotal rows read: " + excelData.size());
        } catch (Exception e) {
            System.err.println("Error reading Excel file: " + e.getMessage());
        }
    }
}
