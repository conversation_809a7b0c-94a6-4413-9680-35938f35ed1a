//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#Relationship
 *
 * <p>RelationshipType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="RelationshipType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionTemporalRelationship"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AllegianceRelationship"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CommandRelation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ForceSideRelation"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskFunctionalRelation"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class RelationshipType {

    @JsonProperty(value = "ActionTemporalRelationship")
    protected ActionTemporalRelationshipType actionTemporalRelationship;
    @JsonProperty(value = "AllegianceRelationship")
    protected AllegianceRelationshipType allegianceRelationship;
    @JsonProperty(value = "CommandRelation")
    protected CommandRelationType commandRelation;
    @JsonProperty(value = "ForceSideRelation")
    protected ForceSideRelationType forceSideRelation;
    @JsonProperty(value = "TaskFunctionalRelation")
    protected TaskFunctionalRelationType taskFunctionalRelation;

    /**
     * 获取actionTemporalRelationship属性的值。
     *
     * @return possible object is
     * {@link ActionTemporalRelationshipType }
     */
    public ActionTemporalRelationshipType getActionTemporalRelationship() {
        return actionTemporalRelationship;
    }

    /**
     * 设置actionTemporalRelationship属性的值。
     *
     * @param value allowed object is
     *              {@link ActionTemporalRelationshipType }
     */
    public void setActionTemporalRelationship(ActionTemporalRelationshipType value) {
        this.actionTemporalRelationship = value;
    }

    /**
     * 获取allegianceRelationship属性的值。
     *
     * @return possible object is
     * {@link AllegianceRelationshipType }
     */
    public AllegianceRelationshipType getAllegianceRelationship() {
        return allegianceRelationship;
    }

    /**
     * 设置allegianceRelationship属性的值。
     *
     * @param value allowed object is
     *              {@link AllegianceRelationshipType }
     */
    public void setAllegianceRelationship(AllegianceRelationshipType value) {
        this.allegianceRelationship = value;
    }

    /**
     * 获取commandRelation属性的值。
     *
     * @return possible object is
     * {@link CommandRelationType }
     */
    public CommandRelationType getCommandRelation() {
        return commandRelation;
    }

    /**
     * 设置commandRelation属性的值。
     *
     * @param value allowed object is
     *              {@link CommandRelationType }
     */
    public void setCommandRelation(CommandRelationType value) {
        this.commandRelation = value;
    }

    /**
     * 获取forceSideRelation属性的值。
     *
     * @return possible object is
     * {@link ForceSideRelationType }
     */
    public ForceSideRelationType getForceSideRelation() {
        return forceSideRelation;
    }

    /**
     * 设置forceSideRelation属性的值。
     *
     * @param value allowed object is
     *              {@link ForceSideRelationType }
     */
    public void setForceSideRelation(ForceSideRelationType value) {
        this.forceSideRelation = value;
    }

    /**
     * 获取taskFunctionalRelation属性的值。
     *
     * @return possible object is
     * {@link TaskFunctionalRelationType }
     */
    public TaskFunctionalRelationType getTaskFunctionalRelation() {
        return taskFunctionalRelation;
    }

    /**
     * 设置taskFunctionalRelation属性的值。
     *
     * @param value allowed object is
     *              {@link TaskFunctionalRelationType }
     */
    public void setTaskFunctionalRelation(TaskFunctionalRelationType value) {
        this.taskFunctionalRelation = value;
    }

}
