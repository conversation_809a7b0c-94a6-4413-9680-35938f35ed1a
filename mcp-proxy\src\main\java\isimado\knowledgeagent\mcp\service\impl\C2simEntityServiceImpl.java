package isimado.knowledgeagent.mcp.service.impl;


import isimado.framework.base.Resp;
import isimado.framework.util.JsonUtils;
import isimado.knowledgeagent.mcp.service.C2simEntityService;
import isimado.knowledgeagent.share.entity.dto.Platform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.JdkClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

import java.net.http.HttpClient;
import java.time.Duration;
import java.util.Map;

@Slf4j
@Service
public class C2simEntityServiceImpl implements C2simEntityService, CommandLineRunner, InitializingBean {


    private RestClient restClient;

    public static void main(String[] args) {
        // 1. 自定义 JDK HttpClient，强制仅用 HTTP/1.1
        HttpClient jdkClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)            // ← 强制 HTTP/1.1，禁用 h2c 升级 :contentReference[oaicite:0]{index=0}
                .connectTimeout(Duration.ofSeconds(3))
                .build();

        // 2. 用该 HttpClient 构造工厂
        JdkClientHttpRequestFactory factory =
                new JdkClientHttpRequestFactory(jdkClient);
        factory.setReadTimeout(1000 * 60 * 5);

        // 3. 构建 RestClient 并发起请求
        RestClient restClient = RestClient.builder()
                .requestFactory(factory)
                .build();

        System.out.println("---- 发起请求 ----");
        long start = System.currentTimeMillis();
        ResponseEntity<Map> response = restClient.get()
                .uri("http://localhost:5678/webhook/searchDoc?input=055")
                .retrieve()
                .toEntity(Map.class);
        log.info("耗时: {}", (System.currentTimeMillis() - start) / 1000.0);
        log.info("收到响应: {}", response.getStatusCode());
        log.info("响应内容: {}", JsonUtils.toJson(response.getBody()));

    }

    @Tool(name = "getC2simEntity", description = "根据武器名称 获取武器的c2sim实体JSON")
    public Platform getC2simEntity(String input) {
        try {
            log.info("getC2simEntity: {}", input);
            Platform platform = restClient.get()
                    .uri("http://localhost:5678/webhook/getC2simEntity?input=" + input)
                    .retrieve()
                    .body(Platform.class);
            log.info("getC2simEntity: success");
            return platform;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 1. 自定义 JDK HttpClient，强制仅用 HTTP/1.1
        HttpClient jdkClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)            // ← 强制 HTTP/1.1，禁用 h2c 升级 :contentReference[oaicite:0]{index=0}
                .connectTimeout(Duration.ofSeconds(3))
                .build();

        // 2. 用该 HttpClient 构造工厂
        JdkClientHttpRequestFactory factory =
                new JdkClientHttpRequestFactory(jdkClient);
        factory.setReadTimeout(Duration.ofMinutes(10));
        restClient = RestClient.builder()
                .baseUrl("http://localhost:5678")
                .requestFactory(factory)
                .build()
        ;

    }

    @Override
    public void run(String... args) throws Exception {
//        Platform platform = this.getC2simEntity("055");
//        System.out.println(platform);
    }
}

