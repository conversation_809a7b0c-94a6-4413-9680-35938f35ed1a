//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * http://www.sisostds.org/ontologies/C2SIM#C2SIMHeader
 *
 * <p>C2SIMHeaderType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="C2SIMHeaderType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CommunicativeActTypeCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ConversationID"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}FromSendingSystem"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}InReplyToMessageID" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}MessageID"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Protocol"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ProtocolVersion"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReplyToSystem" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SecurityClassificationCode" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SendingTime" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ToReceivingSystem"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class C2SIMHeaderType {

    @JsonProperty(value = "CommunicativeActTypeCode", required = true)
    protected CommunicativeActTypeCodeType communicativeActTypeCode;
    @JsonProperty(value = "ConversationID", required = true)
    protected String conversationID;
    @JsonProperty(value = "FromSendingSystem", required = true)
    protected String fromSendingSystem;
    @JsonProperty(value = "InReplyToMessageID")
    protected String inReplyToMessageID;
    @JsonProperty(value = "MessageID", required = true)
    protected String messageID;
    @JsonProperty(value = "Protocol", required = true)
    protected String protocol;
    @JsonProperty(value = "ProtocolVersion", required = true)
    protected String protocolVersion;
    @JsonProperty(value = "ReplyToSystem")
    protected String replyToSystem;
    @JsonProperty(value = "SecurityClassificationCode")
    protected SecurityClassificationCodeType securityClassificationCode;
    @JsonProperty(value = "SendingTime")
    protected DateTimeType sendingTime;
    @JsonProperty(value = "ToReceivingSystem", required = true)
    protected String toReceivingSystem;
}
