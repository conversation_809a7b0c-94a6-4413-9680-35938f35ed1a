//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#ActionTemporalRelationship
 *
 * <p>ActionTemporalRelationshipType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ActionTemporalRelationshipType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ActionTemporalAssociationCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Duration" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TemporalAssociationWithAction"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
public class ActionTemporalRelationshipType {

    @JsonProperty(value = "ActionTemporalAssociationCode", required = true)

    protected ActionTemporalAssociationCodeType actionTemporalAssociationCode;
    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "TemporalAssociationWithAction", required = true)
    protected String temporalAssociationWithAction;

    /**
     * 获取actionTemporalAssociationCode属性的值。
     *
     * @return possible object is
     * {@link ActionTemporalAssociationCodeType }
     */
    public ActionTemporalAssociationCodeType getActionTemporalAssociationCode() {
        return actionTemporalAssociationCode;
    }

    /**
     * 设置actionTemporalAssociationCode属性的值。
     *
     * @param value allowed object is
     *              {@link ActionTemporalAssociationCodeType }
     */
    public void setActionTemporalAssociationCode(ActionTemporalAssociationCodeType value) {
        this.actionTemporalAssociationCode = value;
    }

    /**
     * 获取duration属性的值。
     *
     * @return possible object is
     * {@link DurationType }
     */
    public DurationType getDuration() {
        return duration;
    }

    /**
     * 设置duration属性的值。
     *
     * @param value allowed object is
     *              {@link DurationType }
     */
    public void setDuration(DurationType value) {
        this.duration = value;
    }

    /**
     * 获取temporalAssociationWithAction属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getTemporalAssociationWithAction() {
        return temporalAssociationWithAction;
    }

    /**
     * 设置temporalAssociationWithAction属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTemporalAssociationWithAction(String value) {
        this.temporalAssociationWithAction = value;
    }

}
