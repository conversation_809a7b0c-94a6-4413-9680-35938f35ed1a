package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/7/27 13:41
 */
@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class BasicDataItem {

    @JsonProperty(value = "ParamCode", required = true)
    protected String ParamCode = "";
    @JsonProperty(value = "ParamDesc", required = true)
    protected String ParamDesc = "";
    @JsonProperty(value = "ParamType", required = true)
    protected String ParamType = "";
    @JsonProperty(value = "ParamUnit", required = true)
    protected String ParamUnit = "";
    @JsonProperty(value = "ParamValue", required = true)
    protected String ParamValue = "";
}
