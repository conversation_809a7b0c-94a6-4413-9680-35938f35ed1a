//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#InitializationConcept
 *
 * <p>InitializationConceptType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="InitializationConceptType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}InitializationDataFile"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ObjectDefinitions"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ScenarioSetting"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SystemEntityList"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class InitializationConceptType {

    @JsonProperty(value = "InitializationDataFile")
    protected InitializationDataFileType initializationDataFile;
    @JsonProperty(value = "ObjectDefinitions")
    protected ObjectDefinitionsType objectDefinitions;
    @JsonProperty(value = "ScenarioSetting")
    protected ScenarioSettingType scenarioSetting;
    @JsonProperty(value = "SystemEntityList")
    protected SystemEntityListType systemEntityList;

    /**
     * 获取initializationDataFile属性的值。
     *
     * @return possible object is
     * {@link InitializationDataFileType }
     */
    public InitializationDataFileType getInitializationDataFile() {
        return initializationDataFile;
    }

    /**
     * 设置initializationDataFile属性的值。
     *
     * @param value allowed object is
     *              {@link InitializationDataFileType }
     */
    public void setInitializationDataFile(InitializationDataFileType value) {
        this.initializationDataFile = value;
    }

    /**
     * 获取objectDefinitions属性的值。
     *
     * @return possible object is
     * {@link ObjectDefinitionsType }
     */
    public ObjectDefinitionsType getObjectDefinitions() {
        return objectDefinitions;
    }

    /**
     * 设置objectDefinitions属性的值。
     *
     * @param value allowed object is
     *              {@link ObjectDefinitionsType }
     */
    public void setObjectDefinitions(ObjectDefinitionsType value) {
        this.objectDefinitions = value;
    }

    /**
     * 获取scenarioSetting属性的值。
     *
     * @return possible object is
     * {@link ScenarioSettingType }
     */
    public ScenarioSettingType getScenarioSetting() {
        return scenarioSetting;
    }

    /**
     * 设置scenarioSetting属性的值。
     *
     * @param value allowed object is
     *              {@link ScenarioSettingType }
     */
    public void setScenarioSetting(ScenarioSettingType value) {
        this.scenarioSetting = value;
    }

    /**
     * 获取systemEntityList属性的值。
     *
     * @return possible object is
     * {@link SystemEntityListType }
     */
    public SystemEntityListType getSystemEntityList() {
        return systemEntityList;
    }

    /**
     * 设置systemEntityList属性的值。
     *
     * @param value allowed object is
     *              {@link SystemEntityListType }
     */
    public void setSystemEntityList(SystemEntityListType value) {
        this.systemEntityList = value;
    }

}
