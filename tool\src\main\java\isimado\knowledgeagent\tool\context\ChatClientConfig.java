package isimado.knowledgeagent.tool.context;


import org.springframework.ai.chat.client.ChatClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * 聊天客户端配置类
 */
@Configuration
public class ChatClientConfig {


    /**
     * 配置ChatClient，注册系统指令和工具函数
     */
    @Bean
    public ChatClient chatClient(ChatClient.Builder builder) {
        return builder
                .build();
    }
}
