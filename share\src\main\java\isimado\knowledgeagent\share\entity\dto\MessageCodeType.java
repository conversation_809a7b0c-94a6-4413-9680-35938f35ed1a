//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#MessageCode
 *
 * <p>MessageCodeType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="MessageCodeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AcknowledgeTypeCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}CommunicativeActTypeCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}MIPRequestCategoryCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SystemCommandTypeCode"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TaskStatusCode"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MessageCodeType {

    @JsonProperty(value = "AcknowledgeTypeCode")

    protected AcknowledgeTypeCodeType acknowledgeTypeCode;
    @JsonProperty(value = "CommunicativeActTypeCode")

    protected CommunicativeActTypeCodeType communicativeActTypeCode;
    @JsonProperty(value = "MIPRequestCategoryCode")

    protected MIPRequestCategoryCodeType mipRequestCategoryCode;
    @JsonProperty(value = "SystemCommandTypeCode")

    protected SystemCommandTypeCodeType systemCommandTypeCode;
    @JsonProperty(value = "TaskStatusCode")

    protected TaskStatusCodeType taskStatusCode;

    /**
     * 获取acknowledgeTypeCode属性的值。
     *
     * @return possible object is
     * {@link AcknowledgeTypeCodeType }
     */
    public AcknowledgeTypeCodeType getAcknowledgeTypeCode() {
        return acknowledgeTypeCode;
    }

    /**
     * 设置acknowledgeTypeCode属性的值。
     *
     * @param value allowed object is
     *              {@link AcknowledgeTypeCodeType }
     */
    public void setAcknowledgeTypeCode(AcknowledgeTypeCodeType value) {
        this.acknowledgeTypeCode = value;
    }

    /**
     * 获取communicativeActTypeCode属性的值。
     *
     * @return possible object is
     * {@link CommunicativeActTypeCodeType }
     */
    public CommunicativeActTypeCodeType getCommunicativeActTypeCode() {
        return communicativeActTypeCode;
    }

    /**
     * 设置communicativeActTypeCode属性的值。
     *
     * @param value allowed object is
     *              {@link CommunicativeActTypeCodeType }
     */
    public void setCommunicativeActTypeCode(CommunicativeActTypeCodeType value) {
        this.communicativeActTypeCode = value;
    }

    /**
     * 获取mipRequestCategoryCode属性的值。
     *
     * @return possible object is
     * {@link MIPRequestCategoryCodeType }
     */
    public MIPRequestCategoryCodeType getMIPRequestCategoryCode() {
        return mipRequestCategoryCode;
    }

    /**
     * 设置mipRequestCategoryCode属性的值。
     *
     * @param value allowed object is
     *              {@link MIPRequestCategoryCodeType }
     */
    public void setMIPRequestCategoryCode(MIPRequestCategoryCodeType value) {
        this.mipRequestCategoryCode = value;
    }

    /**
     * 获取systemCommandTypeCode属性的值。
     *
     * @return possible object is
     * {@link SystemCommandTypeCodeType }
     */
    public SystemCommandTypeCodeType getSystemCommandTypeCode() {
        return systemCommandTypeCode;
    }

    /**
     * 设置systemCommandTypeCode属性的值。
     *
     * @param value allowed object is
     *              {@link SystemCommandTypeCodeType }
     */
    public void setSystemCommandTypeCode(SystemCommandTypeCodeType value) {
        this.systemCommandTypeCode = value;
    }

    /**
     * 获取taskStatusCode属性的值。
     *
     * @return possible object is
     * {@link TaskStatusCodeType }
     */
    public TaskStatusCodeType getTaskStatusCode() {
        return taskStatusCode;
    }

    /**
     * 设置taskStatusCode属性的值。
     *
     * @param value allowed object is
     *              {@link TaskStatusCodeType }
     */
    public void setTaskStatusCode(TaskStatusCodeType value) {
        this.taskStatusCode = value;
    }

}
