//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>WeaponRuleOfEngagementCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="WeaponRuleOfEngagementCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ROEFree"/>
 *     &lt;enumeration value="ROEHold"/>
 *     &lt;enumeration value="ROETight"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum WeaponRuleOfEngagementCodeType {

    ROE_FREE("ROEFree"),
    ROE_HOLD("ROEHold"),
    ROE_TIGHT("ROETight");
    private final String value;

    WeaponRuleOfEngagementCodeType(String v) {
        value = v;
    }

    public static WeaponRuleOfEngagementCodeType fromValue(String v) {
        for (WeaponRuleOfEngagementCodeType c : WeaponRuleOfEngagementCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
