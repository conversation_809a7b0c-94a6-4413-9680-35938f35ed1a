package isimado.knowledgeagent.tool.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import isimado.knowledgeagent.tool.entity.dao.C2simEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【c2sim_entity】的数据库操作Mapper
 * @createDate 2025-05-14 15:34:06
 * @Entity isimado.knowledgeagent.domain.entity.dao.C2simEntity
 */
@Mapper
public interface C2simEntityMapper extends BaseMapper<C2simEntity> {

    List<C2simEntity> vectorRetrieval(@Param("embeddingString") String embeddingString, @Param("limit") int limit);
}




