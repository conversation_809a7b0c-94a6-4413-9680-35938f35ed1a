package isimado.knowledgeagent.tool.entity.dao;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import isimado.knowledgeagent.tool.context.mybatis.PGVectorTypeHandler;
import lombok.Data;

/**
 * @TableName c2sim_entity
 */
@TableName(value = "c2sim_entity")
@Data
public class C2simEntity {
    /**
     * 名称
     */
    private String name;

    /**
     * 实体类型
     */
    private String type;

    /**
     *
     */
//    @TableField(typeHandler = JacksonTypeHandler.class)
    private String content;

    /**
     *
     */
    @TableField(typeHandler = PGVectorTypeHandler.class)
    private float[] embedding;


    @TableField(exist = false)
    private double similarity;
}