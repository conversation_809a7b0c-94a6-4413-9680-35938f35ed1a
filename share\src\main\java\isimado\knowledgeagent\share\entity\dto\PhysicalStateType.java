//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/C2SIM#PhysicalState
 *
 * <p>PhysicalStateType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="PhysicalStateType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityStateGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DirectionOfMovement" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityHealthStatus" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Location" maxOccurs="unbounded"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Orientation" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Speed" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class PhysicalStateType {

    @JsonProperty(value = "DateTime")
    protected DateTimeType dateTime;
    @JsonProperty(value = "DirectionOfMovement")
    protected OrientationType directionOfMovement;
    @JsonProperty(value = "EntityHealthStatus")
    protected List<EntityHealthStatusType> entityHealthStatus;
    @JsonProperty(value = "Location", required = true)
    protected List<LocationType> location;
    @JsonProperty(value = "Orientation")
    protected OrientationType orientation;
    @JsonProperty(value = "Speed")
    protected Double speed;

    /**
     * 获取dateTime属性的值。
     *
     * @return possible object is
     * {@link DateTimeType }
     */
    public DateTimeType getDateTime() {
        return dateTime;
    }

    /**
     * 设置dateTime属性的值。
     *
     * @param value allowed object is
     *              {@link DateTimeType }
     */
    public void setDateTime(DateTimeType value) {
        this.dateTime = value;
    }

    /**
     * 获取directionOfMovement属性的值。
     *
     * @return possible object is
     * {@link OrientationType }
     */
    public OrientationType getDirectionOfMovement() {
        return directionOfMovement;
    }

    /**
     * 设置directionOfMovement属性的值。
     *
     * @param value allowed object is
     *              {@link OrientationType }
     */
    public void setDirectionOfMovement(OrientationType value) {
        this.directionOfMovement = value;
    }

    /**
     * Gets the value of the entityHealthStatus property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entityHealthStatus property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEntityHealthStatus().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EntityHealthStatusType }
     */
    public List<EntityHealthStatusType> getEntityHealthStatus() {
        if (entityHealthStatus == null) {
            entityHealthStatus = new ArrayList<EntityHealthStatusType>();
        }
        return this.entityHealthStatus;
    }

    /**
     * Gets the value of the location property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the location property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLocation().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocationType }
     */
    public List<LocationType> getLocation() {
        if (location == null) {
            location = new ArrayList<LocationType>();
        }
        return this.location;
    }

    /**
     * 获取orientation属性的值。
     *
     * @return possible object is
     * {@link OrientationType }
     */
    public OrientationType getOrientation() {
        return orientation;
    }

    /**
     * 设置orientation属性的值。
     *
     * @param value allowed object is
     *              {@link OrientationType }
     */
    public void setOrientation(OrientationType value) {
        this.orientation = value;
    }

    /**
     * 获取speed属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getSpeed() {
        return speed;
    }

    /**
     * 设置speed属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setSpeed(Double value) {
        this.speed = value;
    }

}
