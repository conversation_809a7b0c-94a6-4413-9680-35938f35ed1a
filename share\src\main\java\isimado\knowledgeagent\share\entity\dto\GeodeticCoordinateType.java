//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/C2SIM#GeodeticCoordinate
 *
 * <p>GeodeticCoordinateType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="GeodeticCoordinateType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AltitudeAGL" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}AltitudeMSL" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Latitude"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Longitude"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class GeodeticCoordinateType {

    @JsonProperty(value = "AltitudeAGL")
    protected Double AltitudeAGL;
    @JsonProperty(value = "AltitudeMSL")
    protected Double AltitudeMSL;
    @JsonProperty(value = "Latitude")
    protected double Latitude;
    @JsonProperty(value = "Longitude")
    protected double Longitude;

    /**
     * 获取altitudeAGL属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getAltitudeAGL() {
        return AltitudeAGL;
    }

    /**
     * 设置altitudeAGL属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setAltitudeAGL(Double value) {
        this.AltitudeAGL = value;
    }

    /**
     * 获取altitudeMSL属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getAltitudeMSL() {
        return AltitudeMSL;
    }

    /**
     * 设置altitudeMSL属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setAltitudeMSL(Double value) {
        this.AltitudeMSL = value;
    }

    /**
     * 获取latitude属性的值。
     */
    public double getLatitude() {
        return Latitude;
    }

    /**
     * 设置latitude属性的值。
     */
    public void setLatitude(double value) {
        this.Latitude = value;
    }

    /**
     * 获取longitude属性的值。
     */
    public double getLongitude() {
        return Longitude;
    }

    /**
     * 设置longitude属性的值。
     */
    public void setLongitude(double value) {
        this.Longitude = value;
    }

}
