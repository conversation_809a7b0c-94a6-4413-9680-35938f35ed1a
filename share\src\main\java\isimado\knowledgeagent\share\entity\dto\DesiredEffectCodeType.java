//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


/**
 * <p>DesiredEffectCodeType的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 * <p>
 * <pre>
 * &lt;simpleType name="DesiredEffectCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="TaskSuccess"/>
 *     &lt;enumeration value="BURN"/>
 *     &lt;enumeration value="CAPTRD"/>
 *     &lt;enumeration value="CONS"/>
 *     &lt;enumeration value="DSTRYK"/>
 *     &lt;enumeration value="FKIL"/>
 *     &lt;enumeration value="FLIG"/>
 *     &lt;enumeration value="IDNT"/>
 *     &lt;enumeration value="ILLUMN"/>
 *     &lt;enumeration value="INTREC"/>
 *     &lt;enumeration value="KILL"/>
 *     &lt;enumeration value="LDAM"/>
 *     &lt;enumeration value="LGTRST"/>
 *     &lt;enumeration value="LOST"/>
 *     &lt;enumeration value="MKIL"/>
 *     &lt;enumeration value="MODDAM"/>
 *     &lt;enumeration value="NBCAS"/>
 *     &lt;enumeration value="NKN"/>
 *     &lt;enumeration value="NORSTN"/>
 *     &lt;enumeration value="NOS"/>
 *     &lt;enumeration value="NUTRLD"/>
 *     &lt;enumeration value="SDAM"/>
 *     &lt;enumeration value="SUPRSD"/>
 *     &lt;enumeration value="VLNRST"/>
 *     &lt;enumeration value="VRBPRT"/>
 *     &lt;enumeration value="WNDD"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */

public enum DesiredEffectCodeType {

    TASK_SUCCESS("TaskSuccess"),
    BURN("BURN"),
    CAPTRD("CAPTRD"),
    CONS("CONS"),
    DSTRYK("DSTRYK"),
    FKIL("FKIL"),
    FLIG("FLIG"),
    IDNT("IDNT"),
    ILLUMN("ILLUMN"),
    INTREC("INTREC"),
    KILL("KILL"),
    LDAM("LDAM"),
    LGTRST("LGTRST"),
    LOST("LOST"),
    MKIL("MKIL"),
    MODDAM("MODDAM"),
    NBCAS("NBCAS"),
    NKN("NKN"),
    NORSTN("NORSTN"),
    NOS("NOS"),
    NUTRLD("NUTRLD"),
    SDAM("SDAM"),
    SUPRSD("SUPRSD"),
    VLNRST("VLNRST"),
    VRBPRT("VRBPRT"),
    WNDD("WNDD");
    private final String value;

    DesiredEffectCodeType(String v) {
        value = v;
    }

    public static DesiredEffectCodeType fromValue(String v) {
        for (DesiredEffectCodeType c : DesiredEffectCodeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public String value() {
        return value;
    }

}
