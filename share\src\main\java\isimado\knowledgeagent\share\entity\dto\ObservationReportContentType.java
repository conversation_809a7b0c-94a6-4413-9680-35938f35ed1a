//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/smx#ObservationReportContent
 *
 * <p>ObservationReportContentType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="ObservationReportContentType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReportContentGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Observation" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class ObservationReportContentType {

    @JsonProperty(value = "Duration")
    protected DurationType duration;
    @JsonProperty(value = "TimeOfObservation", required = true)
    protected TimeInstantType timeOfObservation;
    @JsonProperty(value = "Observation", required = true)
    protected List<ObservationType> observation;

    /**
     * 获取duration属性的值。
     *
     * @return possible object is
     * {@link DurationType }
     */
    public DurationType getDuration() {
        return duration;
    }

    /**
     * 设置duration属性的值。
     *
     * @param value allowed object is
     *              {@link DurationType }
     */
    public void setDuration(DurationType value) {
        this.duration = value;
    }

    /**
     * 获取timeOfObservation属性的值。
     *
     * @return possible object is
     * {@link TimeInstantType }
     */
    public TimeInstantType getTimeOfObservation() {
        return timeOfObservation;
    }

    /**
     * 设置timeOfObservation属性的值。
     *
     * @param value allowed object is
     *              {@link TimeInstantType }
     */
    public void setTimeOfObservation(TimeInstantType value) {
        this.timeOfObservation = value;
    }

    /**
     * Gets the value of the observation property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the observation property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getObservation().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ObservationType }
     */
    public List<ObservationType> getObservation() {
        if (observation == null) {
            observation = new ArrayList<ObservationType>();
        }
        return this.observation;
    }

}
