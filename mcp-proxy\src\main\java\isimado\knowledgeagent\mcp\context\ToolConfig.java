package isimado.knowledgeagent.mcp.context;

import isimado.knowledgeagent.mcp.service.C2simEntityService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ToolConfig {


    /**
     * @param c2simEntityService
     * @return 工具回调提供者
     */
    @Bean
    public ToolCallbackProvider bookToolCallbackProvider(C2simEntityService c2simEntityService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(c2simEntityService)
                .build();
    }
}
