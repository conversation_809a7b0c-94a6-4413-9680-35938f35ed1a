//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/smx#UnitSymbol
 *
 * <p>UnitSymbolType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="UnitSymbolType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PhysicalEntityGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}HigherFormation" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}OperationalStatus" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ReinforcedReducedType" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}SpecialC2HQ" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class UnitSymbolType implements PhysicalEntity {

    @JsonProperty(value = "EntityType", required = true)
    protected List<EntityTypeType> entityType;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "CurrentState", required = true)
    protected EntityStateType currentState;
    @JsonProperty(value = "Marking")
    protected String marking;
    @JsonProperty(value = "HigherFormation")
    protected String higherFormation;
    @JsonProperty(value = "OperationalStatus")
    protected OperationalStatusType operationalStatus;
    @JsonProperty(value = "ReinforcedReducedType")

    protected ReinforcedReducedTypeType reinforcedReducedType;
    @JsonProperty(value = "SpecialC2HQ")
    protected String specialC2HQ;

    /**
     * Gets the value of the entityType property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entityType property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEntityType().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EntityTypeType }
     */
    public List<EntityTypeType> getEntityType() {
        if (entityType == null) {
            entityType = new ArrayList<EntityTypeType>();
        }
        return this.entityType;
    }

    /**
     * 获取name属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * 设置name属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * 获取uuid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getUUID() {
        return uuid;
    }

    /**
     * 设置uuid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUUID(String value) {
        this.uuid = value;
    }

    /**
     * 获取currentState属性的值。
     *
     * @return possible object is
     * {@link EntityStateType }
     */
    public EntityStateType getCurrentState() {
        return currentState;
    }

    /**
     * 设置currentState属性的值。
     *
     * @param value allowed object is
     *              {@link EntityStateType }
     */
    public void setCurrentState(EntityStateType value) {
        this.currentState = value;
    }

    /**
     * 获取marking属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getMarking() {
        return marking;
    }

    /**
     * 设置marking属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setMarking(String value) {
        this.marking = value;
    }

    @Override
    public String getOriginID() {
        return null;
    }

    @Override
    public void setOriginID(String value) {

    }

    @Override
    public List<BasicDataItem> getBasicDataItem() {
        return null;
    }

    @Override
    public void setBasicDataItem(List<BasicDataItem> basicDataItem) {

    }

    /**
     * 获取higherFormation属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getHigherFormation() {
        return higherFormation;
    }

    /**
     * 设置higherFormation属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHigherFormation(String value) {
        this.higherFormation = value;
    }

    /**
     * 获取operationalStatus属性的值。
     *
     * @return possible object is
     * {@link OperationalStatusType }
     */
    public OperationalStatusType getOperationalStatus() {
        return operationalStatus;
    }

    /**
     * 设置operationalStatus属性的值。
     *
     * @param value allowed object is
     *              {@link OperationalStatusType }
     */
    public void setOperationalStatus(OperationalStatusType value) {
        this.operationalStatus = value;
    }

    /**
     * 获取reinforcedReducedType属性的值。
     *
     * @return possible object is
     * {@link ReinforcedReducedTypeType }
     */
    public ReinforcedReducedTypeType getReinforcedReducedType() {
        return reinforcedReducedType;
    }

    /**
     * 设置reinforcedReducedType属性的值。
     *
     * @param value allowed object is
     *              {@link ReinforcedReducedTypeType }
     */
    public void setReinforcedReducedType(ReinforcedReducedTypeType value) {
        this.reinforcedReducedType = value;
    }

    /**
     * 获取specialC2HQ属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSpecialC2HQ() {
        return specialC2HQ;
    }

    /**
     * 设置specialC2HQ属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSpecialC2HQ(String value) {
        this.specialC2HQ = value;
    }

}
