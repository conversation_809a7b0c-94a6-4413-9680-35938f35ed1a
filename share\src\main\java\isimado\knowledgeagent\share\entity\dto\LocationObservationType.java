//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST 
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * http://www.sisostds.org/ontologies/smx#LocationObservation
 *
 * <p>LocationObservationType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="LocationObservationType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}ObservationGroup"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}DirectionOfMovement" minOccurs="0"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Location"/>
 *         &lt;element ref="{http://www.sisostds.org/schemas/C2SIM/1.1}Speed" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class LocationObservationType {

    @JsonProperty(value = "ActorReference")
    protected String actorReference;
    @JsonProperty(value = "ConfidenceLevel")
    protected Double confidenceLevel;
    @JsonProperty(value = "UncertaintyInterval")
    protected Double uncertaintyInterval;
    @JsonProperty(value = "DirectionOfMovement")
    protected OrientationType directionOfMovement;
    @JsonProperty(value = "Location", required = true)
    protected LocationType location;
    @JsonProperty(value = "Speed")
    protected Double speed;

    /**
     * 获取actorReference属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getActorReference() {
        return actorReference;
    }

    /**
     * 设置actorReference属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setActorReference(String value) {
        this.actorReference = value;
    }

    /**
     * 获取confidenceLevel属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getConfidenceLevel() {
        return confidenceLevel;
    }

    /**
     * 设置confidenceLevel属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setConfidenceLevel(Double value) {
        this.confidenceLevel = value;
    }

    /**
     * 获取uncertaintyInterval属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getUncertaintyInterval() {
        return uncertaintyInterval;
    }

    /**
     * 设置uncertaintyInterval属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setUncertaintyInterval(Double value) {
        this.uncertaintyInterval = value;
    }

    /**
     * 获取directionOfMovement属性的值。
     *
     * @return possible object is
     * {@link OrientationType }
     */
    public OrientationType getDirectionOfMovement() {
        return directionOfMovement;
    }

    /**
     * 设置directionOfMovement属性的值。
     *
     * @param value allowed object is
     *              {@link OrientationType }
     */
    public void setDirectionOfMovement(OrientationType value) {
        this.directionOfMovement = value;
    }

    /**
     * 获取location属性的值。
     *
     * @return possible object is
     * {@link LocationType }
     */
    public LocationType getLocation() {
        return location;
    }

    /**
     * 设置location属性的值。
     *
     * @param value allowed object is
     *              {@link LocationType }
     */
    public void setLocation(LocationType value) {
        this.location = value;
    }

    /**
     * 获取speed属性的值。
     *
     * @return possible object is
     * {@link Double }
     */
    public Double getSpeed() {
        return speed;
    }

    /**
     * 设置speed属性的值。
     *
     * @param value allowed object is
     *              {@link Double }
     */
    public void setSpeed(Double value) {
        this.speed = value;
    }

}
