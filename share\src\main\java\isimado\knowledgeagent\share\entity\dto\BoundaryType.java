//
// 此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.2.8-b130911.1802 生成的
// 请访问 <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// 在重新编译源模式时, 对此文件的所有修改都将丢失。
// 生成时间: 2022.06.29 时间 07:20:37 PM CST
//

package isimado.knowledgeagent.share.entity.dto;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


/**
 * http://www.sisostds.org/ontologies/smx#Boundary
 *
 * <p>BoundaryType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="BoundaryType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}EntityGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}PhysicalEntityGroup"/>
 *         &lt;group ref="{http://www.sisostds.org/schemas/C2SIM/1.1}TacticalGraphicGroup"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class BoundaryType implements PhysicalEntity {

    @JsonProperty(value = "EntityType", required = true)
    protected List<EntityTypeType> entityType;
    @JsonProperty(value = "Name")
    protected String name;
    @JsonProperty(value = "UUID", required = true)
    protected String uuid;
    @JsonProperty(value = "CurrentState", required = true)
    protected EntityStateType currentState;
    @JsonProperty(value = "Marking")
    protected String marking;
    @JsonProperty(value = "Owner")
    protected String owner;

    /**
     * Gets the value of the entityType property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entityType property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEntityType().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link EntityTypeType }
     */
    @Override
    public List<EntityTypeType> getEntityType() {
        if (entityType == null) {
            entityType = new ArrayList<>();
        }
        return this.entityType;
    }

    @Override
    public String getUUID() {
        return uuid;
    }

    @Override
    public String getOriginID() {
        return null;
    }

    @Override
    public void setOriginID(String value) {
        // Implementation required by interface
    }

    @Override
    public List<BasicDataItem> getBasicDataItem() {
        return null;
    }

    @Override
    public void setBasicDataItem(List<BasicDataItem> basicDataItem) {
        // Implementation required by interface
    }
}
